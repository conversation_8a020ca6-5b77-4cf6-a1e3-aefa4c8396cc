const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, EmbedBuilder, ChannelType } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('listchannels')
        .setDescription('List all text channels in the server with their IDs and names'),

    async execute(interaction) {
        try {
            // Get all text channels in the guild
            const textChannels = interaction.guild.channels.cache
                .filter(channel => channel.type === ChannelType.GuildText)
                .sort((a, b) => a.position - b.position);

            if (textChannels.size === 0) {
                return interaction.reply({
                    content: 'No text channels found in this server.',
                    ephemeral: true
                });
            }

            // Create embed with channel list
            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle('📋 Server Text Channels')
                .setDescription('Here are all the text channels in this server:')
                .setFooter({ text: `Total: ${textChannels.size} channels` })
                .setTimestamp();

            // Split channels into chunks to avoid embed field limits
            const channelsArray = [...textChannels.values()];
            const chunksSize = 10;
            const chunks = [];

            for (let i = 0; i < channelsArray.length; i += chunksSize) {
                chunks.push(channelsArray.slice(i, i + chunksSize));
            }

            // Add fields for each chunk
            chunks.forEach((chunk, index) => {
                const fieldValue = chunk.map(channel => {
                    // Show channel mention, name, and ID
                    const channelMention = `<#${channel.id}>`;
                    const channelName = channel.name;
                    const channelId = channel.id;
                    
                    // Check if channel name has special characters
                    const hasSpecialChars = /[^\x00-\x7F]/.test(channelName);
                    const specialCharIndicator = hasSpecialChars ? ' 🌐' : '';
                    
                    return `${channelMention} \`${channelName}\`${specialCharIndicator}\nID: \`${channelId}\``;
                }).join('\n\n');

                embed.addFields({
                    name: `Channels ${index * chunksSize + 1}-${Math.min((index + 1) * chunksSize, channelsArray.length)}`,
                    value: fieldValue,
                    inline: false
                });
            });

            // Add legend
            embed.addFields({
                name: '📖 Legend',
                value: '🌐 = Contains special characters (may cause display issues)\n`Channel Name` = Raw channel name\nID = Channel ID for manual selection',
                inline: false
            });

            await interaction.reply({
                embeds: [embed],
                ephemeral: true
            });

        } catch (error) {
            console.error('Error in listchannels command:', error);
            await interaction.reply({
                content: 'An error occurred while fetching the channel list.',
                ephemeral: true
            });
        }
    }
};
