# Advanced Moderation Commands

A comprehensive set of moderation tools for Discord servers.

## 🛡️ **Warning System**

### `/warn <user> <reason> [dm]`
Issue warnings to users with automatic escalation.

**Features:**
- **Auto-escalation**: 3 warnings = 24h timeout, 5 warnings = ban
- **Warning tracking**: Persistent warning history
- **DM notifications**: Optional user notifications
- **Unique IDs**: Each warning gets a unique identifier

**Usage:**
```
/warn user:@BadUser reason:"Spam in chat" dm:true
```

**Auto-Actions:**
- 3+ warnings → 24-hour timeout
- 5+ warnings → Automatic ban

### `/warnings <user> [show_details]`
View user warning history with interactive management.

**Features:**
- **Summary view**: Quick overview with warning level
- **Detailed view**: Full warning history with moderators
- **Interactive buttons**: Clear warnings, toggle views
- **Warning levels**: Color-coded risk assessment

**Usage:**
```
/warnings user:@User show_details:true
```

## 🔨 **Advanced Banning**

### `/tempban <user> <duration> [reason] [delete_messages]`
Temporarily ban users with automatic unbanning.

**Features:**
- **Flexible duration**: `30m`, `2h`, `1d`, `1w` formats
- **Auto-unban**: Automatic removal when time expires
- **Message cleanup**: Delete messages from last X days
- **DM notifications**: User gets notified with unban time

**Usage:**
```
/tempban user:@BadUser duration:2d reason:"Harassment" delete_messages:1
```

**Duration Formats:**
- `30m` = 30 minutes
- `2h` = 2 hours  
- `1d` = 1 day
- `1w` = 1 week

## 🧹 **Message Management**

### `/purge <amount> [user] [filter] [contains]`
Advanced bulk message deletion with smart filtering.

**Features:**
- **Smart filtering**: Bots, users, attachments, links, embeds
- **User-specific**: Delete only from specific users
- **Content filtering**: Delete messages containing specific text
- **Safety limits**: Respects Discord's 14-day limit

**Usage:**
```
/purge amount:50 user:@SpamBot filter:bots
/purge amount:20 contains:"bad word"
/purge amount:100 filter:attachments
```

**Filters:**
- `all` - All messages
- `bots` - Bot messages only
- `users` - User messages only
- `attachments` - Messages with files
- `links` - Messages with URLs
- `embeds` - Messages with embeds

### `/slowmode <seconds> [reason]`
Set channel slowmode with smart duration formatting.

**Features:**
- **Flexible timing**: 0-21600 seconds (6 hours max)
- **Smart display**: Human-readable duration format

**Usage:**
```
/slowmode seconds:30 reason:"Calm down chat"
/slowmode seconds:0 reason:"Remove slowmode"
```

## 🔒 **Server Security**

### `/lockdown <action> [reason]`
Lock/unlock server or individual channels.

**Features:**
- **Server-wide**: Lock all channels at once
- **Channel-specific**: Lock individual channels
- **Bulk operations**: Efficient mass channel management
- **Permission restoration**: Smart unlock that restores original permissions

**Usage:**
```
/lockdown action:lock_server reason:"Emergency situation"
/lockdown action:unlock_channel reason:"Issue resolved"
```

**Actions:**
- `lock_server` - Lock entire server
- `unlock_server` - Unlock entire server
- `lock_channel` - Lock current channel
- `unlock_channel` - Unlock current channel

## 👤 **User Management**

### `/nickname set <user> [nickname]`
Manage user nicknames with moderation controls.

**Features:**
- **Set nicknames**: Change user display names
- **Remove nicknames**: Reset to username
- **Permission checks**: Respects role hierarchy

**Usage:**
```
/nickname set user:@User nickname:"New Name"
/nickname set user:@User (removes nickname)
```

### `/nickname reset <user>`
Reset user nickname to their username.

### `/nickname cleanup <filter>`
Mass cleanup of problematic nicknames.

**Features:**
- **Smart detection**: Finds problematic nicknames automatically
- **Multiple filters**: Different cleanup criteria
- **Bulk processing**: Clean many nicknames at once
- **Detailed reporting**: Shows what was cleaned and why

**Filters:**
- `special` - Remove special characters
- `invisible` - Remove invisible/zero-width characters
- `hoisting` - Fix names that hoist to top of member list
- `all` - Clean all detected issues

**Usage:**
```
/nickname cleanup filter:hoisting
/nickname cleanup filter:all
```

## 📊 **Integration Features**



### **Permission System**
Commands respect Discord's permission hierarchy:
- Cannot moderate users with equal/higher roles
- Checks bot permissions before actions
- Validates user permissions for each command

### **Data Persistence**
- **Warnings**: Stored in `src/data/warnings.json`
- **Temporary bans**: Stored in `src/data/tempbans.json`
- **Auto-cleanup**: Old data automatically managed

### **Error Handling**
Comprehensive error handling for:
- Permission issues
- Invalid targets
- Discord API errors
- File system errors

## 🎯 **Permission Requirements**

| Command | Required Permission |
|---------|-------------------|
| `/warn` | Moderate Members |
| `/warnings` | Moderate Members |
| `/tempban` | Ban Members |
| `/purge` | Manage Messages |
| `/slowmode` | Manage Channels |
| `/lockdown` | Administrator |
| `/nickname` | Manage Nicknames |

## 🚀 **Quick Start Guide**

1. **Issue your first warning**:
   ```
   /warn user:@BadUser reason:"Please follow the rules"
   ```

2. **Clean up spam**:
   ```
   /purge amount:50 filter:bots
   ```

3. **Set slowmode during busy periods**:
   ```
   /slowmode seconds:10 reason:"High activity"
   ```

4. **Emergency lockdown**:
   ```
   /lockdown action:lock_server reason:"Emergency"
   ```

## 🔧 **Advanced Usage**

### **Warning Escalation Strategy**
1. **1st offense**: Warning + education
2. **2nd offense**: Warning + reminder
3. **3rd offense**: Auto-timeout (24h) + final warning
4. **4th offense**: Manual review
5. **5th offense**: Auto-ban

### **Tempban Best Practices**
- **Minor issues**: 1-6 hours
- **Moderate issues**: 1-3 days  
- **Serious issues**: 1-2 weeks
- **Severe issues**: Consider permanent ban

### **Purge Strategies**
- **Spam cleanup**: Use user filter
- **Bot cleanup**: Use bot filter
- **Content cleanup**: Use contains filter
- **Media cleanup**: Use attachments filter

## 📈 **Moderation Analytics**

Track your server's moderation activity:
- Warning trends and patterns
- Most common infractions
- Moderator activity levels
- Effectiveness of auto-actions



## 🛠️ **Troubleshooting**

### **Commands not working?**
1. Check bot permissions
2. Verify user permissions
3. Check role hierarchy
4. Review error messages

### **Auto-actions not triggering?**
1. Verify warning thresholds
2. Check bot permissions for timeout/ban
3. Review error messages

### **Tempbans not auto-unbanning?**
1. Check bot uptime
2. Verify file permissions
3. Review tempban data file

## 🔮 **Future Enhancements**

Planned improvements:
- Custom warning thresholds per server
- Scheduled moderation actions
- Advanced analytics dashboard
- Integration with external moderation tools
- Machine learning for auto-moderation
