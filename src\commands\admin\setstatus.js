const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setstatus')
        .setDescription('Change the bot\'s status')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addStringOption(option =>
            option.setName('status')
                .setDescription('The status to set')
                .setRequired(true)
                .addChoices(
                    { name: '🟢 Online', value: 'online' },
                    { name: '🟡 Idle', value: 'idle' },
                    { name: '🔴 Do Not Disturb', value: 'dnd' },
                    { name: '⚫ Invisible', value: 'invisible' }
                ))
        .addStringOption(option =>
            option.setName('activity-type')
                .setDescription('The type of activity')
                .setRequired(true)
                .addChoices(
                    { name: '🎮 Playing', value: '0' },
                    { name: '🎥 Streaming', value: '1' },
                    { name: '🎧 Listening', value: '2' },
                    { name: '👀 Watching', value: '3' },
                    { name: '🏆 Competing', value: '5' }
                ))
        .addStringOption(option =>
            option.setName('activity-name')
                .setDescription('The activity name')
                .setRequired(true)),

    async execute(interaction, client) {
        const status = interaction.options.getString('status');
        const activityType = parseInt(interaction.options.getString('activity-type'));
        const activityName = interaction.options.getString('activity-name');

        try {
            const presence = {
                status: status,
                activities: [{
                    name: activityName,
                    type: activityType
                }]
            };

            // Update the presence
            await client.user.setPresence(presence);

            const statusEmoji = {
                'online': '🟢',
                'idle': '🟡',
                'dnd': '🔴',
                'invisible': '⚫'
            };

            const activityTypeMap = {
                0: 'Playing',
                1: 'Streaming',
                2: 'Listening to',
                3: 'Watching',
                5: 'Competing in'
            };

            await interaction.reply({
                content: `Status updated successfully!\n${statusEmoji[status]} **Status:** ${status}\n🎮 **Activity:** ${activityTypeMap[activityType]} ${activityName}`,
                flags: 64  // This makes it ephemeral without using the deprecated option
            });
        } catch (error) {
            console.error('Error setting presence:', error);
            await interaction.reply({
                content: '❌ Failed to update status. Please check the console for errors.',
                flags: 64
            });
        }
    }
};

