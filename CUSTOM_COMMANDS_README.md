# Custom Commands System

This Discord bot now includes a comprehensive custom commands system that allows users to create, manage, and execute custom slash commands.

## Features

- **Create Custom Commands**: Users can create custom slash commands with personalized responses
- **List Commands**: View all custom commands with pagination
- **Edit Commands**: Modify existing custom commands (only by creator or admins)
- **Delete Commands**: Remove custom commands with confirmation (only by creator or admins)
- **Autocomplete**: Smart autocomplete for command names when editing/deleting
- **Placeholders**: Support for dynamic placeholders in command responses
- **Permissions**: Permission-based command creation and management

## Commands

### `/command create`
Creates a new custom command through an interactive modal.

**Requirements:**
- User must have "Manage Messages" permission
- Command name must be unique (not conflict with existing commands)
- Command name must be 1-32 characters, lowercase letters, numbers, hyphens, and underscores only

### `/command list`
Displays all custom commands with pagination (10 commands per page).

**Features:**
- Shows command name, description, creator, and creation date
- Navigation buttons for multiple pages
- Ephemeral response (only visible to the user)

### `/command edit <command_name>`
Edits an existing custom command through an interactive modal.

**Requirements:**
- User must be the command creator OR have Administrator permissions
- Command must exist

### `/command delete <command_name>`
Deletes an existing custom command with confirmation.

**Requirements:**
- User must be the command creator OR have Administrator permissions
- Command must exist
- Confirmation required within 30 seconds

## Placeholders

Custom command responses support the following placeholders:

- `{user}` - Mentions the user who executed the command
- `{username}` - The username of the user who executed the command
- `{server}` - The name of the server/guild
- `{channel}` - Mentions the channel where the command was executed

**Example:**
```
Response: "Hello {user}! Welcome to {server}. You're currently in {channel}."
Result: "Hello @JohnDoe! Welcome to My Server. You're currently in #general."
```

## File Structure

```
src/
├── commands/utility/command.js          # Main command file
├── functions/handlers/handleCustomCommands.js  # Custom command handler
├── data/customCommands.json             # Storage for custom commands
└── events/client/interactionCreate.js   # Updated interaction handler
```

## Data Storage

Custom commands are stored in `src/data/customCommands.json` with the following structure:

```json
{
  "commandname": {
    "description": "Command description",
    "response": "Command response with {placeholders}",
    "createdBy": "user_id",
    "createdAt": 1234567890000,
    "usageCount": 5,
    "lastUsed": 1234567890000,
    "updatedBy": "user_id",
    "updatedAt": 1234567890000
  }
}
```

## Permissions

### Command Creation
- Requires "Manage Messages" permission
- Can be modified in the `handleCreateCommandModal` function

### Command Editing/Deletion
- Command creator can always edit/delete their commands
- Users with Administrator permission can edit/delete any command

## Installation

The custom commands system is automatically loaded when the bot starts. No additional setup is required.

## Usage Examples

1. **Creating a welcome command:**
   ```
   /command create
   Name: welcome
   Description: Welcome new members
   Response: Welcome to {server}, {user}! Please read the rules in #rules.
   ```

2. **Creating an info command:**
   ```
   /command create
   Name: info
   Description: Server information
   Response: This is {server}! We have many channels for you to explore.
   ```

3. **Using placeholders:**
   ```
   Response: Hey {username}! Thanks for using this command in {channel}.
   ```

## Error Handling

The system includes comprehensive error handling for:
- Invalid command names
- Permission denied scenarios
- File system errors
- Discord API errors
- Command conflicts

## Limitations

- Command names must be unique across all commands (including built-in commands)
- Maximum 32 characters for command names
- Maximum 100 characters for descriptions
- Maximum 2000 characters for responses
- Commands are registered globally with Discord (may take up to 1 hour to appear)

## Troubleshooting

### Commands not appearing
- Wait up to 1 hour for Discord to register new commands
- Check console for registration errors
- Ensure bot has proper permissions

### Permission errors
- Verify user has required permissions
- Check if user is command creator or has Administrator role

### File errors
- Ensure `src/data/` directory exists and is writable
- Check file permissions for `customCommands.json`

## Future Enhancements

Potential improvements that could be added:
- Command categories/tags
- Usage statistics and analytics
- Command cooldowns
- Role-based command restrictions
- Import/export functionality
- Command aliases
- Rich embed responses
- Scheduled command execution
