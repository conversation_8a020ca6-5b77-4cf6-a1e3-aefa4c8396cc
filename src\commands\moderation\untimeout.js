const { SlashCommandBuilder, PermissionsBitField } = require("discord.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("untimeout")
    .setDescription("Remove a timeout from a user.")
    .addUserOption((option) =>
      option
        .setName("target")
        .setDescription("The user to remove the timeout from")
        .setRequired(true)
    )
    .addStringOption((option) =>
      option
        .setName("reason")
        .setDescription("Reason for removing the timeout")
    ),

  async execute(interaction) {
    // Check if the user has the "ModerateMembers" permission
    if (!interaction.member.permissions.has(PermissionsBitField.Flags.ModerateMembers)) {
      return interaction.reply({
        content: "❌ You do not have permission to use this command.",
        ephemeral: true,
      });
    }

    // Check if the bot has the "ModerateMembers" permission
    if (!interaction.guild.members.me.permissions.has(PermissionsBitField.Flags.ModerateMembers)) {
      return interaction.reply({
        content: "❌ I do not have permission to remove timeouts.",
        ephemeral: true,
      });
    }

    const user = interaction.options.getUser("target");
    const reason = interaction.options.getString("reason") || "No reason provided.";

    // Fetch the member to untimeout
    const member = interaction.guild.members.cache.get(user.id);

    if (!member) {
      return interaction.reply({
        content: "❌ The specified user is not in this server.",
        ephemeral: true,
      });
    }

    if (!member.moderatable) {
      return interaction.reply({
        content: "❌ I cannot moderate this member. Ensure my role is higher than theirs and I have the proper permissions.",
        ephemeral: true,
      });
    }

    // Check if the user is already not timed out
    if (!member.isCommunicationDisabled()) {
      return interaction.reply({
        content: "❌ This user is not currently timed out.",
        ephemeral: true,
      });
    }

    try {
      // Remove the timeout
      await member.timeout(null, reason);

      await interaction.reply({
        content: `✅ Successfully removed timeout from ${user.tag}. Reason: ${reason}`,
        ephemeral: true, // Ensure this is set to true
      });
    } catch (error) {
      console.error(error);
      await interaction.reply({
        content: "❌ An error occurred while trying to remove the timeout.",
        ephemeral: true,
      });
    }
  },
};