const { REST } = require("@discordjs/rest");
const { Routes } = require("discord.js");
const fs = require("fs");
const path = require("path");

module.exports = (client) => {
  client.handleCommands = async () => {
    // Initialize commands collection and array
    client.commands = new Map();
    client.commandArray = [];
    let loadedCommandsCount = 0;

    console.log("🔄 Loading commands from local files...");

    // Read command folders
    const commandFolders = fs
      .readdirSync("./src/commands")
      .filter((folder) => fs.statSync(`./src/commands/${folder}`).isDirectory()); // Only get directories

    // Load commands from each folder
    for (const folder of commandFolders) {
      const commandFiles = fs
        .readdirSync(`./src/commands/${folder}`)
        .filter((file) => file.endsWith(".js"));

      for (const file of commandFiles) {
        try {
          const command = require(path.join(__dirname, `../../commands/${folder}/${file}`));

          // Validate command structure
          if (!command?.data?.name) {
            console.error(`⚠️ Error: Missing "data.name" in ${folder}/${file}. Skipping.`);
            continue;
          }

          // Validate command data can be converted to JSON
          try {
            command.data.toJSON();
          } catch (jsonError) {
            console.error(`⚠️ Error: Invalid command data in ${folder}/${file}:`, jsonError.message);
            continue;
          }

          // Add command to collections
          client.commands.set(command.data.name, command);
          client.commandArray.push(command.data.toJSON());
          loadedCommandsCount++;
          console.log(`✅ Command Loaded: ${command.data.name} (${folder})`);
        } catch (error) {
          console.error(`❌ Error loading command ${folder}/${file}:`, error.message);
        }
      }
    }

    console.log(`📊 Total commands loaded locally: ${loadedCommandsCount}`);

    // Load environment variables
    const clientId = process.env.DISCORD_CLIENT_ID;
    const guildIds = process.env.DISCORD_GUILD_IDS ? process.env.DISCORD_GUILD_IDS.split(',').filter(id => id.trim()) : [];

    // Check for missing environment variables
    if (!clientId || !process.env.TOKEN) {
      console.error("❌ Missing environment variables. Check your .env file.");
      console.error("Required: TOKEN, DISCORD_CLIENT_ID");
      console.error("Optional: DISCORD_GUILD_IDS (comma-separated)");
      console.log("🔄 Bot will continue running with local commands only.");
      return;
    }

    // Register commands with Discord
    await registerCommandsWithDiscord(client, clientId, guildIds, loadedCommandsCount);
  };

  // Separate function for Discord command registration with enhanced error handling
  async function registerCommandsWithDiscord(client, clientId, guildIds, totalCommands) {
    const rest = new REST({ version: "10" }).setToken(process.env.TOKEN);
    let registrationSuccess = false;

    // First, validate bot permissions
    const permissionsValid = await validateBotPermissions(rest, clientId, guildIds);
    if (!permissionsValid) {
      console.log("⚠️ Permission validation failed. Proceeding with registration attempt anyway...");
    }

    try {
      // If guild IDs are provided, try guild registration first
      if (guildIds.length > 0) {
        console.log(`🔄 Attempting to register ${totalCommands} commands for ${guildIds.length} guild(s)...`);

        let successfulGuilds = 0;
        let failedGuilds = [];

        for (const guildId of guildIds) {
          const guildRegistrationSuccess = await registerCommandsForGuild(rest, clientId, guildId.trim(), client.commandArray, totalCommands);

          if (guildRegistrationSuccess) {
            successfulGuilds++;
            console.log(`✅ Successfully registered commands for guild: ${guildId}`);
          } else {
            failedGuilds.push({ guildId });
          }
        }

        if (successfulGuilds > 0) {
          registrationSuccess = true;
          console.log(`✅ Commands registered successfully for ${successfulGuilds}/${guildIds.length} guilds.`);
        }

        // If some guilds failed, try global registration as fallback
        if (failedGuilds.length > 0) {
          console.log(`⚠️ ${failedGuilds.length} guild(s) failed. Attempting global registration as fallback...`);
          const globalSuccess = await attemptGlobalRegistration(rest, clientId, client.commandArray, totalCommands);
          if (globalSuccess) {
            registrationSuccess = true;
          }
        }
      } else {
        // No guild IDs provided, register globally
        const globalSuccess = await attemptGlobalRegistration(rest, clientId, client.commandArray, totalCommands);
        if (globalSuccess) {
          registrationSuccess = true;
        }
      }

    } catch (error) {
      console.error("❌ Unexpected error during command registration:", error.message);
    }

    if (!registrationSuccess) {
      await handleRegistrationFailure(totalCommands);
    }
  }

  // Function to register commands for a specific guild with retry logic
  async function registerCommandsForGuild(rest, clientId, guildId, commandArray, totalCommands) {
    const maxRetries = 2;
    const baseDelay = 1000;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await rest.put(Routes.applicationGuildCommands(clientId, guildId), {
          body: commandArray,
        });
        return true;

      } catch (guildError) {
        console.error(`❌ Failed to register commands for guild ${guildId} (Attempt ${attempt}/${maxRetries}):`, guildError.message);

        // Don't retry on permission errors
        if (guildError.code === 50001) {
          console.error(`🔒 Permission Error for guild ${guildId}: The bot lacks the 'applications.commands' scope or proper permissions.`);
          return false;
        }

        // Handle rate limits
        if (guildError.code === 429) {
          const retryAfter = guildError.retry_after || 3;
          console.error(`⏱️ Rate limited for guild ${guildId}. Waiting ${retryAfter} seconds...`);
          await sleep(retryAfter * 1000);
          continue;
        }

        // For other errors, use exponential backoff
        if (attempt < maxRetries) {
          const delay = baseDelay * Math.pow(2, attempt - 1);
          console.log(`⏳ Retrying guild ${guildId} in ${delay}ms...`);
          await sleep(delay);
        }
      }
    }

    return false;
  }

  // Function to attempt global command registration with retry logic
  async function attemptGlobalRegistration(rest, clientId, commandArray, totalCommands) {
    const maxRetries = 3;
    const baseDelay = 1000; // 1 second

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🌍 Registering ${totalCommands} commands globally... (Attempt ${attempt}/${maxRetries})`);

        await rest.put(Routes.applicationCommands(clientId), {
          body: commandArray,
        });

        console.log("✅ Successfully registered commands globally!");
        return true;

      } catch (globalError) {
        console.error(`❌ Global command registration failed (Attempt ${attempt}/${maxRetries}):`, globalError.message);

        // Don't retry on permission errors
        if (globalError.code === 50001) {
          console.error("🔒 Permission Error: The bot lacks the 'applications.commands' scope.");
          console.error("📋 Please re-invite the bot with the correct permissions.");
          return false;
        }

        // Don't retry on rate limit errors with specific handling
        if (globalError.code === 429) {
          const retryAfter = globalError.retry_after || 5;
          console.error(`⏱️ Rate limited. Waiting ${retryAfter} seconds before retry...`);
          await sleep(retryAfter * 1000);
          continue;
        }

        // For other errors, use exponential backoff
        if (attempt < maxRetries) {
          const delay = baseDelay * Math.pow(2, attempt - 1);
          console.log(`⏳ Retrying in ${delay}ms...`);
          await sleep(delay);
        } else {
          console.error("❌ All retry attempts failed for global registration.");
          return false;
        }
      }
    }

    return false;
  }

  // Helper function for delays
  function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Function to validate bot permissions before registration
  async function validateBotPermissions(rest, clientId, guildIds) {
    console.log("🔍 Validating bot permissions...");

    try {
      // Try to fetch the application info to validate token and client ID
      const application = await rest.get(Routes.oauth2CurrentApplication());

      if (application.id !== clientId) {
        console.error("❌ CLIENT_ID mismatch! The provided DISCORD_CLIENT_ID doesn't match the token.");
        return false;
      }

      console.log(`✅ Bot application validated: ${application.name}`);

      // If guild IDs are provided, validate bot presence in guilds
      if (guildIds.length > 0) {
        console.log(`🔍 Checking bot presence in ${guildIds.length} guild(s)...`);

        for (const guildId of guildIds) {
          try {
            // Try to fetch guild commands to test permissions
            await rest.get(Routes.applicationGuildCommands(clientId, guildId.trim()));
            console.log(`✅ Bot has access to guild: ${guildId}`);
          } catch (guildError) {
            if (guildError.code === 50001) {
              console.error(`❌ Bot lacks permissions in guild: ${guildId}`);
              console.error("💡 The bot may not be in the guild or lacks 'applications.commands' scope.");
            } else if (guildError.code === 10004) {
              console.error(`❌ Guild not found: ${guildId}`);
              console.error("💡 Check if the guild ID is correct and the bot is in the guild.");
            } else {
              console.error(`⚠️ Unknown error for guild ${guildId}:`, guildError.message);
            }
          }
        }
      }

      return true;

    } catch (error) {
      console.error("❌ Failed to validate bot permissions:", error.message);

      if (error.code === 401) {
        console.error("🔒 Invalid bot token. Please check your TOKEN in the .env file.");
      }

      return false;
    }
  }

  // Function to handle registration failure
  async function handleRegistrationFailure(totalCommands) {
    console.log("\n" + "=".repeat(60));
    console.log("⚠️  COMMAND REGISTRATION FAILED");
    console.log("=".repeat(60));
    console.log(`✅ ${totalCommands} commands loaded locally and ready to use`);
    console.log("❌ Commands NOT registered with Discord (won't appear in slash command list)");
    console.log("\n🔧 TO FIX THIS ISSUE:");
    console.log("1. Check your bot's permissions in Discord Developer Portal");
    console.log("2. Ensure the bot has 'applications.commands' scope");
    console.log("3. Re-invite the bot with proper permissions");
    console.log("4. Verify DISCORD_CLIENT_ID and TOKEN in .env file");
    console.log("\n🤖 Bot will continue running with limited functionality.");
    console.log("=".repeat(60) + "\n");
  }
};