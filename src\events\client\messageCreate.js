const { Events } = require('discord.js');

module.exports = {
    name: Events.MessageCreate,
    execute(message) {
        if (message.author.bot) return; // Ignore bot messages
        
        ; // Add this to debug

        // Original Arabic greeting
        if (message.content.toLowerCase() === 'السلام عليكم') {
            message.reply('وعليكم السلام ورحمة الله وبركاته');
        };
          if (message.content.toLowerCase() === 'مساء الخير') {
            message.reply('مساء الورد');
        };
        if (message.content.toLowerCase() === 'صباح الخير') {
            message.reply('صباح الورد');
        }

        // Commands that start with !
        if (message.content.startsWith('!')) {
            const command = message.content.toLowerCase();

            switch (command) {

                case '!مساء الخير':
                    message.reply('مساء الورد');
                    break;
                    
                case '!صباح الخير':
                    message.reply('صباح الورد');
                    break;
                    
                case '!دعم':
                    message.reply('https://tip.dokan.sa/sahm');
                    break;

                case '!x':
                    message.reply('https://x.com/4e56');
                    break;

                case '!اكس':
                    message.reply('https://x.com/4e56');
                    break;

                case '!تويتر':
                    message.reply('https://x.com/4e56');
                    break;

                case '!كيك':
                    message.reply('https://kick.com/ssahm');
                    break;

                case '!kick':
                    message.reply('https://kick.com/ssahm');
                    break;

                case '!حسابات':
                    message.reply('https://guns.lol/.sahm');
                    break;

                case '!ص':
                    message.reply('اللهم صل وسلم على سيدنا محمد');
                    break;

                    
                case '!كفاره':
                    message.reply('سُبْحَانَكَ اللّهُمَّ وَبِحَمْدِكَ، أَشْهَدُ أَنْ لا إِلَهَ إِلاَّ أَنْتَ، أَسْتَغْفِرُكَ وَأَتُوبُ إِلَيْكَ.');
                    break;  

                case '!كفارة':
                    message.reply('سُبْحَانَكَ اللّهُمَّ وَبِحَمْدِكَ، أَشْهَدُ أَنْ لا إِلَهَ إِلاَّ أَنْتَ، أَسْتَغْفِرُكَ وَأَتُوبُ إِلَيْكَ.');
                    break;  
                                       
                case '!احبك':
                    message.reply('ماحبيت الا اللي يحبك ويغليك');
                    break;  
                    
                        
                case '!س':
                    message.reply('سبحان الله وبحمده سبحان الله العظيم');
                    break;
                    
                case '!صلاه':
                    message.channel.send(' الصلاة يا عباد الله، اللي ما صلى يصلي');
                    break;

                case '!صلاة':
                    message.channel.send(' الصلاة يا عباد الله، اللي ما صلى يصلي');
                    break;
            }
        }
    }
};
