const { Events } = require('discord.js');

module.exports = {
    name: Events.MessageCreate,
    execute(message) {
        if (message.author.bot) return; // Ignore bot messages
        
        ; // Add this to debug

        // Original Arabic greeting
        if (message.content.toLowerCase() === 'السلام عليكم') {
            message.reply('وعليكم السلام ورحمة الله وبركاته');
        };
          if (message.content.toLowerCase() === 'مساء الخير') {
            message.reply('مساء الورد');
        };
        if (message.content.toLowerCase() === 'صباح الخير') {
            message.reply('صباح الورد');
        }

        // Commands that start with !
        if (message.content.startsWith('!')) {
            const command = message.content.toLowerCase();

            switch (command) {

                case '!مساء الخير':
                    message.reply('مساء الورد');
                    break;
                    
                case '!صباح الخير':
                    message.reply('صباح الورد');
                    break;
                    
                case '!دعم':
                    message.reply('https://tip.dokan.sa/sahm');
                    break;

                case '!ص':
                    message.reply('اللهم صل وسلم على سيدنا محمد');
                    break;

                    
                case '!كفاره':
                    message.reply('أَسْتَغْفِرُ اللَّهَ الْعَظِيمَ الَّذِي لَا إِلَهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ ، وَأَتُوبُ إِلَيْهِ');
                    break;  

                                       
                case '!احبك':
                    message.reply('ماحبيت الا اللي يحبك ويغليك');
                    break;  
                    
                        
                case '!س':
                    message.reply('سبحان الله وبحمده سبحان الله العظيم');
                    break;  
            }
        }
    }
};
