const { <PERSON>lash<PERSON><PERSON>mandB<PERSON>er, EmbedBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('slowmode')
        .setDescription('Set slowmode for the current channel')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageChannels)
        .addIntegerOption(option =>
            option.setName('seconds')
                .setDescription('Slowmode duration in seconds (0 to disable, max 21600)')
                .setRequired(true)
                .setMinValue(0)
                .setMaxValue(21600))
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for setting slowmode')
                .setRequired(false)
                .setMaxLength(200)),

    async execute(interaction) {
        const seconds = interaction.options.getInteger('seconds');
        const reason = interaction.options.getString('reason') || 'No reason provided';

        // Check bot permissions
        if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageChannels)) {
            return interaction.reply({
                content: '❌ I need the "Manage Channels" permission to set slowmode.',
                ephemeral: true
            });
        }

        try {
            // Set the slowmode
            await interaction.channel.setRateLimitPerUser(seconds, reason);

            // Create embed based on action
            let embed;
            
            if (seconds === 0) {
                // Slowmode disabled
                embed = new EmbedBuilder()
                    .setColor('#4CAF50')
                    .setTitle('✅ Slowmode Disabled')
                    .setDescription(`Slowmode has been disabled in ${interaction.channel}.`)
                    .addFields(
                        { name: 'Moderator', value: `${interaction.user.tag}`, inline: true },
                        { name: 'Reason', value: reason, inline: false }
                    )
                    .setTimestamp();
            } else {
                // Slowmode enabled
                const duration = formatDuration(seconds);
                
                embed = new EmbedBuilder()
                    .setColor('#FFA500')
                    .setTitle('⏰ Slowmode Enabled')
                    .setDescription(`Slowmode has been set to **${duration}** in ${interaction.channel}.`)
                    .addFields(
                        { name: 'Duration', value: `${seconds} seconds`, inline: true },
                        { name: 'Moderator', value: `${interaction.user.tag}`, inline: true },
                        { name: 'Reason', value: reason, inline: false }
                    )
                    .setTimestamp();
            }



            await interaction.reply({ embeds: [embed] });

        } catch (error) {
            console.error('Error in slowmode command:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff6b6b')
                .setTitle('❌ Slowmode Failed')
                .setDescription('An error occurred while trying to set slowmode.')
                .addFields(
                    { name: 'Error', value: error.message || 'Unknown error', inline: false }
                )
                .setTimestamp();

            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    }
};

// Format duration in a human-readable way
function formatDuration(seconds) {
    if (seconds < 60) {
        return `${seconds} second${seconds !== 1 ? 's' : ''}`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        if (remainingSeconds === 0) {
            return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
        } else {
            return `${minutes} minute${minutes !== 1 ? 's' : ''} ${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`;
        }
    } else {
        const hours = Math.floor(seconds / 3600);
        const remainingMinutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = seconds % 60;
        
        let result = `${hours} hour${hours !== 1 ? 's' : ''}`;
        if (remainingMinutes > 0) {
            result += ` ${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;
        }
        if (remainingSeconds > 0) {
            result += ` ${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`;
        }
        return result;
    }
}
