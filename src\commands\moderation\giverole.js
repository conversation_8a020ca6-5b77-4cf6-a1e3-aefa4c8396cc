const { SlashCommandBuilder } = require('@discordjs/builders');
const { PermissionsBitField } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('giverole')
        .setDescription('Assigns a role to a user (Mods only)')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The user to assign the role to')
                .setRequired(true))
        .addRoleOption(option =>
            option.setName('role')
                .setDescription('The role to assign to the user')
                .setRequired(true)),
    async execute(interaction) {
        const user = interaction.options.getUser('user');
        const role = interaction.options.getRole('role');
        const member = interaction.guild.members.cache.get(user.id);

        // Check if the user invoking the command has the "Manage Roles" permission
        if (!interaction.member.permissions.has(PermissionsBitField.Flags.ManageRoles)) {
            return interaction.reply({ content: '❌ You do not have permission to use this command.', ephemeral: true });
        }

        // Check if the bot has permission to manage roles
        if (!interaction.guild.members.me.permissions.has(PermissionsBitField.Flags.ManageRoles)) {
            return interaction.reply({ content: '❌ I do not have permission to manage roles.', ephemeral: true });
        }

        // Check if the role is manageable by the bot
        if (interaction.guild.members.me.roles.highest.position <= role.position) {
            return interaction.reply({ content: '❌ I cannot assign this role because it is higher than my highest role.', ephemeral: true });
        }

        // Check if the role is manageable by the user
        if (interaction.member.roles.highest.position <= role.position) {
            return interaction.reply({ content: '❌ You cannot assign this role because it is higher than your highest role.', ephemeral: true });
        }

        // Attempt to add the role
        try {
            await member.roles.add(role);
            await interaction.reply({ content: `✅ Successfully assigned the role ${role.name} to ${user.tag}.`, ephemeral: true });
        } catch (error) {
            console.error(error);
            await interaction.reply({ content: '❌ An error occurred while assigning the role.', ephemeral: true });
        }
    },
};
