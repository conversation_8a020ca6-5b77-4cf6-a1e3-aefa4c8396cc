const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder() // Corrected from 'date' to 'data'
        .setName("embed")
        .setDescription('Return the bot embed!'),

    async execute(interaction, client) { // Corrected 'exectue' to 'execute'
        const embed = new EmbedBuilder()
        .setTitle(`This is an EMBED!`)
        .setDescription('This is very cool description!')
        .setColor(0x18e1ee)
        .setImage(client.user.displayAvatarURL()) // Corrected the typo here
        .setThumbnail(client.user.displayAvatarURL()) // Corrected the typo here
        .setTimestamp(Date.now())
        .setAuthor({
            url: `https://kick.com/d7px`,
            iconURL: interaction.user.displayAvatarURL(),
            name: interaction.user.tag
        })
        .setFooter({
            iconURL: client.user.displayAvatarURL(), // Corrected the typo here
            text: client.user.tag
        })
        .setURL(`https://kick.com/d7px`)
        .addFields([
            {
                name: `Field 1`,
                value: `Field value 1`,
                inline: true
            },
            {
                name: `Field 2`,
                value: `Field value 2`,
                inline: true
            }
        ]);
    

           await interaction.reply({
            embeds: [embed]
           });
    },
};