const { SlashCommandBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder() // Corrected from 'date' to 'data'
        .setName("ping")
        .setDescription('Return the bot ping!'),

    async execute(interaction, client) { // Corrected 'exectue' to 'execute'
        const message = await interaction.deferReply({
          fetchReply: true // Corrected 'ferchReply' to 'fetchReply'
        });

        const newMessage = `API Latency: ${client.ws.ping}\nClient Ping: ${message.createdTimestamp - interaction.createdTimestamp}`;
        await interaction.editReply({ // Corrected 'edtiReplt' to 'editReply'
            content: newMessage
        });
    }
};
