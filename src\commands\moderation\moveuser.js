const { SlashCommandBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('moveuser')
        .setDescription('Move a user to another voice channel.')
        .addUserOption(option => 
            option.setName('user')
                .setDescription('The user to move')
                .setRequired(true))
        .addChannelOption(option => 
            option.setName('channel')
                .setDescription('The target voice channel')
                .setRequired(true)),
    async execute(interaction) {
        if (!interaction.member.permissions.has('ADMINISTRATOR')) {
            return interaction.reply({ content: "You don't have permission to use this command.", ephemeral: true });
        }

        const user = interaction.options.getMember('user');
        const channel = interaction.options.getChannel('channel');

        if (!channel.isVoice()) {
            return interaction.reply({ content: "Please select a voice channel.", ephemeral: true });
        }

        await user.voice.setChannel(channel);
        interaction.reply({ content: `${user} has been moved to ${channel}.`, ephemeral: true });
    }
};
