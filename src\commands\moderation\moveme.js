const { SlashCommandBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('moveme')
        .setDescription('Move yourself to another voice channel.')
        .addChannelOption(option => 
            option.setName('channel')
                .setDescription('The target voice channel')
                .setRequired(true)),
    async execute(interaction) {
        const channel = interaction.options.getChannel('channel');

        if (!channel.isVoice()) {
            return interaction.reply({ content: "Please select a voice channel.", ephemeral: true });
        }

        await interaction.member.voice.setChannel(channel);
        interaction.reply({ content: `You have been moved to ${channel}.`, ephemeral: true });
    }
};
