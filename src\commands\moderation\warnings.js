const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits } = require('discord.js');
const fs = require('fs');
const path = require('path');

const WARNINGS_FILE = path.join(__dirname, '../../data/warnings.json');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('warnings')
        .setDescription('View warnings for a user')
        .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers)
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The user to view warnings for')
                .setRequired(true))
        .addBooleanOption(option =>
            option.setName('show_details')
                .setDescription('Show detailed warning information')
                .setRequired(false)),

    async execute(interaction) {
        const user = interaction.options.getUser('user');
        const showDetails = interaction.options.getBoolean('show_details') ?? false;

        try {
            const warnings = loadWarnings();
            const guildId = interaction.guild.id;
            const userId = user.id;

            const userWarnings = warnings[guildId]?.[userId] || [];

            if (userWarnings.length === 0) {
                const embed = new EmbedBuilder()
                    .setColor('#4CAF50')
                    .setTitle('✅ No Warnings Found')
                    .setDescription(`${user.tag} has no warnings on record.`)
                    .setThumbnail(user.displayAvatarURL({ dynamic: true }))
                    .setTimestamp();

                return interaction.reply({ embeds: [embed], ephemeral: true });
            }

            // Sort warnings by timestamp (newest first)
            userWarnings.sort((a, b) => b.timestamp - a.timestamp);

            const embed = new EmbedBuilder()
                .setColor('#FFA500')
                .setTitle(`⚠️ Warnings for ${user.tag}`)
                .setDescription(`Total warnings: **${userWarnings.length}**`)
                .setThumbnail(user.displayAvatarURL({ dynamic: true }))
                .setTimestamp();

            if (showDetails) {
                // Show detailed warnings (limit to 10 most recent)
                const recentWarnings = userWarnings.slice(0, 10);
                
                for (let i = 0; i < recentWarnings.length; i++) {
                    const warning = recentWarnings[i];
                    const moderator = await interaction.client.users.fetch(warning.moderator).catch(() => null);
                    const moderatorName = moderator ? moderator.tag : 'Unknown Moderator';
                    
                    embed.addFields({
                        name: `Warning #${i + 1} (ID: ${warning.id})`,
                        value: `**Reason:** ${warning.reason}\n**Moderator:** ${moderatorName}\n**Date:** <t:${Math.floor(warning.timestamp / 1000)}:R>`,
                        inline: false
                    });
                }

                if (userWarnings.length > 10) {
                    embed.setFooter({ text: `Showing 10 most recent warnings out of ${userWarnings.length} total` });
                }
            } else {
                // Show summary
                const recentWarning = userWarnings[0];
                const moderator = await interaction.client.users.fetch(recentWarning.moderator).catch(() => null);
                const moderatorName = moderator ? moderator.tag : 'Unknown Moderator';

                embed.addFields(
                    { name: 'Most Recent Warning', value: recentWarning.reason, inline: false },
                    { name: 'Issued By', value: moderatorName, inline: true },
                    { name: 'Date', value: `<t:${Math.floor(recentWarning.timestamp / 1000)}:R>`, inline: true }
                );

                // Warning level indicator
                let warningLevel = '';
                if (userWarnings.length >= 5) {
                    warningLevel = '🔴 **CRITICAL** - Auto-ban threshold reached';
                } else if (userWarnings.length >= 3) {
                    warningLevel = '🟠 **HIGH** - Auto-timeout threshold reached';
                } else if (userWarnings.length >= 2) {
                    warningLevel = '🟡 **MEDIUM** - Multiple warnings';
                } else {
                    warningLevel = '🟢 **LOW** - Single warning';
                }

                embed.addFields({ name: 'Warning Level', value: warningLevel, inline: false });
            }

            // Create action buttons
            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`clear_warnings_${userId}`)
                        .setLabel('Clear All Warnings')
                        .setStyle(ButtonStyle.Danger)
                        .setEmoji('🗑️'),
                    new ButtonBuilder()
                        .setCustomId(`warning_details_${userId}`)
                        .setLabel(showDetails ? 'Show Summary' : 'Show Details')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('📋')
                );

            const response = await interaction.reply({ 
                embeds: [embed], 
                components: [row],
                ephemeral: true 
            });

            // Handle button interactions
            const collector = response.createMessageComponentCollector({
                time: 300000 // 5 minutes
            });

            collector.on('collect', async (i) => {
                if (i.user.id !== interaction.user.id) {
                    return i.reply({ content: 'You cannot interact with this warning panel.', ephemeral: true });
                }

                if (i.customId === `clear_warnings_${userId}`) {
                    // Clear warnings confirmation
                    const confirmEmbed = new EmbedBuilder()
                        .setColor('#ff6b6b')
                        .setTitle('⚠️ Confirm Warning Clearance')
                        .setDescription(`Are you sure you want to clear **all ${userWarnings.length} warnings** for ${user.tag}?\n\n**This action cannot be undone!**`)
                        .setTimestamp();

                    const confirmRow = new ActionRowBuilder()
                        .addComponents(
                            new ButtonBuilder()
                                .setCustomId(`confirm_clear_${userId}`)
                                .setLabel('Yes, Clear All')
                                .setStyle(ButtonStyle.Danger),
                            new ButtonBuilder()
                                .setCustomId('cancel_clear')
                                .setLabel('Cancel')
                                .setStyle(ButtonStyle.Secondary)
                        );

                    await i.update({ embeds: [confirmEmbed], components: [confirmRow] });

                } else if (i.customId === `warning_details_${userId}`) {
                    // Toggle between summary and details view
                    const newShowDetails = !showDetails;
                    
                    // Re-run the command logic with toggled view
                    const newEmbed = new EmbedBuilder()
                        .setColor('#FFA500')
                        .setTitle(`⚠️ Warnings for ${user.tag}`)
                        .setDescription(`Total warnings: **${userWarnings.length}**`)
                        .setThumbnail(user.displayAvatarURL({ dynamic: true }))
                        .setTimestamp();

                    if (newShowDetails) {
                        const recentWarnings = userWarnings.slice(0, 10);
                        
                        for (let j = 0; j < recentWarnings.length; j++) {
                            const warning = recentWarnings[j];
                            const moderator = await interaction.client.users.fetch(warning.moderator).catch(() => null);
                            const moderatorName = moderator ? moderator.tag : 'Unknown Moderator';
                            
                            newEmbed.addFields({
                                name: `Warning #${j + 1} (ID: ${warning.id})`,
                                value: `**Reason:** ${warning.reason}\n**Moderator:** ${moderatorName}\n**Date:** <t:${Math.floor(warning.timestamp / 1000)}:R>`,
                                inline: false
                            });
                        }

                        if (userWarnings.length > 10) {
                            newEmbed.setFooter({ text: `Showing 10 most recent warnings out of ${userWarnings.length} total` });
                        }
                    } else {
                        const recentWarning = userWarnings[0];
                        const moderator = await interaction.client.users.fetch(recentWarning.moderator).catch(() => null);
                        const moderatorName = moderator ? moderator.tag : 'Unknown Moderator';

                        newEmbed.addFields(
                            { name: 'Most Recent Warning', value: recentWarning.reason, inline: false },
                            { name: 'Issued By', value: moderatorName, inline: true },
                            { name: 'Date', value: `<t:${Math.floor(recentWarning.timestamp / 1000)}:R>`, inline: true }
                        );

                        let warningLevel = '';
                        if (userWarnings.length >= 5) {
                            warningLevel = '🔴 **CRITICAL** - Auto-ban threshold reached';
                        } else if (userWarnings.length >= 3) {
                            warningLevel = '🟠 **HIGH** - Auto-timeout threshold reached';
                        } else if (userWarnings.length >= 2) {
                            warningLevel = '🟡 **MEDIUM** - Multiple warnings';
                        } else {
                            warningLevel = '🟢 **LOW** - Single warning';
                        }

                        newEmbed.addFields({ name: 'Warning Level', value: warningLevel, inline: false });
                    }

                    const newRow = new ActionRowBuilder()
                        .addComponents(
                            new ButtonBuilder()
                                .setCustomId(`clear_warnings_${userId}`)
                                .setLabel('Clear All Warnings')
                                .setStyle(ButtonStyle.Danger)
                                .setEmoji('🗑️'),
                            new ButtonBuilder()
                                .setCustomId(`warning_details_${userId}`)
                                .setLabel(newShowDetails ? 'Show Summary' : 'Show Details')
                                .setStyle(ButtonStyle.Secondary)
                                .setEmoji('📋')
                        );

                    await i.update({ embeds: [newEmbed], components: [newRow] });

                } else if (i.customId === `confirm_clear_${userId}`) {
                    // Clear the warnings
                    delete warnings[guildId][userId];
                    
                    if (saveWarnings(warnings)) {
                        const successEmbed = new EmbedBuilder()
                            .setColor('#4CAF50')
                            .setTitle('✅ Warnings Cleared')
                            .setDescription(`Successfully cleared all warnings for ${user.tag}.`)
                            .setTimestamp();



                        await i.update({ embeds: [successEmbed], components: [] });
                    } else {
                        const errorEmbed = new EmbedBuilder()
                            .setColor('#ff6b6b')
                            .setTitle('❌ Error')
                            .setDescription('Failed to clear warnings. Please try again.')
                            .setTimestamp();

                        await i.update({ embeds: [errorEmbed], components: [] });
                    }

                } else if (i.customId === 'cancel_clear') {
                    await i.update({ embeds: [embed], components: [row] });
                }
            });

            collector.on('end', async () => {
                try {
                    await response.edit({ components: [] });
                } catch (error) {
                    // Message might have been deleted
                }
            });

        } catch (error) {
            console.error('Error in warnings command:', error);
            await interaction.reply({
                content: '❌ An error occurred while fetching warnings.',
                ephemeral: true
            });
        }
    }
};

// Load warnings from JSON file
function loadWarnings() {
    try {
        if (!fs.existsSync(WARNINGS_FILE)) {
            return {};
        }
        const data = fs.readFileSync(WARNINGS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading warnings:', error);
        return {};
    }
}

// Save warnings to JSON file
function saveWarnings(warnings) {
    try {
        const dataDir = path.dirname(WARNINGS_FILE);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }
        
        fs.writeFileSync(WARNINGS_FILE, JSON.stringify(warnings, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving warnings:', error);
        return false;
    }
}
