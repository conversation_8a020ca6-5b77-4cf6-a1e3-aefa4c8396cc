const { SlashCommandBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('mute')
        .setDescription('Mute a user in a voice channel.')
        .addUserOption(option => 
            option.setName('user')
                .setDescription('The user to mute')
                .setRequired(true)),
    async execute(interaction) {
        if (!interaction.member.permissions.has('ADMINISTRATOR')) {
            return interaction.reply({ content: "You don't have permission to use this command.", ephemeral: true });
        }

        const user = interaction.options.getMember('user');
        if (!user.voice.channel) {
            return interaction.reply({ content: "User is not in a voice channel.", ephemeral: true });
        }

        await user.voice.setMute(true);
        interaction.reply({ content: `${user} has been muted.`, ephemeral: true });
    }
};

