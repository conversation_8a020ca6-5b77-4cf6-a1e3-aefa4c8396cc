const { SlashCommandBuilder } = require('discord.js');
const { getVoiceConnection } = require('@discordjs/voice');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('leave')
        .setDescription('Leaves the voice channel'),
    async execute(interaction) {
        const connection = getVoiceConnection(interaction.guild.id);
        if (!connection) return interaction.reply({ content: 'I am not in a voice channel.', ephemeral: true });

        connection.destroy();
        await interaction.reply({ content: 'Left the voice channel.', ephemeral: true });
    },
};
