const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Embed<PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');
const fs = require('fs');
const path = require('path');

const TEMPBANS_FILE = path.join(__dirname, '../../data/tempbans.json');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('tempban')
        .setDescription('Temporarily ban a user')
        .setDefaultMemberPermissions(PermissionFlagsBits.BanMembers)
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The user to temporarily ban')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('duration')
                .setDescription('Ban duration (e.g., 1h, 2d, 1w)')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for the temporary ban')
                .setRequired(false)
                .setMaxLength(500))
        .addIntegerOption(option =>
            option.setName('delete_messages')
                .setDescription('Delete messages from the last X days (0-7)')
                .setRequired(false)
                .setMinValue(0)
                .setMaxValue(7)),

    async execute(interaction) {
        const user = interaction.options.getUser('user');
        const durationStr = interaction.options.getString('duration');
        const reason = interaction.options.getString('reason') || 'No reason provided';
        const deleteMessages = interaction.options.getInteger('delete_messages') || 0;

        // Parse duration
        const duration = parseDuration(durationStr);
        if (!duration) {
            return interaction.reply({
                content: '❌ Invalid duration format! Use formats like: `1h`, `2d`, `1w`, `30m`\n\nExamples:\n• `30m` = 30 minutes\n• `2h` = 2 hours\n• `1d` = 1 day\n• `1w` = 1 week',
                ephemeral: true
            });
        }

        // Check if user is trying to ban themselves
        if (user.id === interaction.user.id) {
            return interaction.reply({
                content: '❌ You cannot ban yourself!',
                ephemeral: true
            });
        }

        // Check if user is trying to ban a bot
        if (user.bot) {
            return interaction.reply({
                content: '❌ You cannot ban bots!',
                ephemeral: true
            });
        }

        // Check if user is in the guild
        const member = interaction.guild.members.cache.get(user.id);
        if (member) {
            // Check if user is trying to ban someone with higher or equal permissions
            if (member.roles.highest.position >= interaction.member.roles.highest.position) {
                return interaction.reply({
                    content: '❌ You cannot ban someone with equal or higher permissions!',
                    ephemeral: true
                });
            }

            // Check if user is bannable
            if (!member.bannable) {
                return interaction.reply({
                    content: '❌ I cannot ban this user! They may have higher permissions than me.',
                    ephemeral: true
                });
            }
        }

        try {
            // Calculate unban time
            const unbanTime = Date.now() + duration;
            const unbanDate = new Date(unbanTime);

            // Ban the user
            await interaction.guild.members.ban(user, {
                deleteMessageDays: deleteMessages,
                reason: `Temporary ban: ${reason} | Duration: ${durationStr} | Moderator: ${interaction.user.tag}`
            });

            // Store tempban data
            const tempbans = loadTempbans();
            if (!tempbans[interaction.guild.id]) {
                tempbans[interaction.guild.id] = {};
            }

            tempbans[interaction.guild.id][user.id] = {
                userId: user.id,
                moderator: interaction.user.id,
                reason: reason,
                duration: durationStr,
                bannedAt: Date.now(),
                unbanAt: unbanTime,
                guildId: interaction.guild.id
            };

            saveTempbans(tempbans);

            // Create success embed
            const embed = new EmbedBuilder()
                .setColor('#8B0000')
                .setTitle('🔨 User Temporarily Banned')
                .addFields(
                    { name: 'User', value: `${user.tag} (${user.id})`, inline: true },
                    { name: 'Moderator', value: `${interaction.user.tag}`, inline: true },
                    { name: 'Duration', value: durationStr, inline: true },
                    { name: 'Unban Date', value: `<t:${Math.floor(unbanTime / 1000)}:F>`, inline: true },
                    { name: 'Unban Time', value: `<t:${Math.floor(unbanTime / 1000)}:R>`, inline: true },
                    { name: 'Messages Deleted', value: `${deleteMessages} day${deleteMessages !== 1 ? 's' : ''}`, inline: true },
                    { name: 'Reason', value: reason, inline: false }
                )
                .setThumbnail(user.displayAvatarURL({ dynamic: true }))
                .setTimestamp();

            // Send DM to user
            try {
                const dmEmbed = new EmbedBuilder()
                    .setColor('#8B0000')
                    .setTitle(`🔨 Temporarily Banned from ${interaction.guild.name}`)
                    .addFields(
                        { name: 'Duration', value: durationStr, inline: true },
                        { name: 'Unban Date', value: `<t:${Math.floor(unbanTime / 1000)}:F>`, inline: true },
                        { name: 'Reason', value: reason, inline: false },
                        { name: 'Moderator', value: interaction.user.tag, inline: true }
                    )
                    .setFooter({ text: 'You will be automatically unbanned when the duration expires.' })
                    .setTimestamp();

                await user.send({ embeds: [dmEmbed] });
                embed.setFooter({ text: '✅ User has been notified via DM' });
            } catch (error) {
                embed.setFooter({ text: '⚠️ Could not send DM to user' });
            }



            await interaction.reply({ embeds: [embed] });

            // Schedule automatic unban
            scheduleUnban(interaction.client, interaction.guild.id, user.id, unbanTime);

        } catch (error) {
            console.error('Error in tempban command:', error);
            await interaction.reply({
                content: '❌ An error occurred while trying to temporarily ban the user.',
                ephemeral: true
            });
        }
    }
};

// Parse duration string to milliseconds
function parseDuration(durationStr) {
    const regex = /^(\d+)([smhdw])$/i;
    const match = durationStr.match(regex);
    
    if (!match) return null;
    
    const value = parseInt(match[1]);
    const unit = match[2].toLowerCase();
    
    const multipliers = {
        's': 1000,           // seconds
        'm': 60 * 1000,      // minutes
        'h': 60 * 60 * 1000, // hours
        'd': 24 * 60 * 60 * 1000, // days
        'w': 7 * 24 * 60 * 60 * 1000 // weeks
    };
    
    return value * multipliers[unit];
}

// Load tempbans from JSON file
function loadTempbans() {
    try {
        if (!fs.existsSync(TEMPBANS_FILE)) {
            return {};
        }
        const data = fs.readFileSync(TEMPBANS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading tempbans:', error);
        return {};
    }
}

// Save tempbans to JSON file
function saveTempbans(tempbans) {
    try {
        const dataDir = path.dirname(TEMPBANS_FILE);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }
        
        fs.writeFileSync(TEMPBANS_FILE, JSON.stringify(tempbans, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving tempbans:', error);
        return false;
    }
}

// Schedule automatic unban
function scheduleUnban(client, guildId, userId, unbanTime) {
    const delay = unbanTime - Date.now();
    
    if (delay <= 0) {
        // Should unban immediately
        performUnban(client, guildId, userId);
        return;
    }
    
    // Schedule unban
    setTimeout(() => {
        performUnban(client, guildId, userId);
    }, delay);
}

// Perform the unban
async function performUnban(client, guildId, userId) {
    try {
        const guild = client.guilds.cache.get(guildId);
        if (!guild) return;

        // Check if user is still banned
        const bans = await guild.bans.fetch();
        if (!bans.has(userId)) return;

        // Unban the user
        await guild.members.unban(userId, 'Temporary ban expired');

        // Remove from tempbans
        const tempbans = loadTempbans();
        if (tempbans[guildId] && tempbans[guildId][userId]) {
            delete tempbans[guildId][userId];
            saveTempbans(tempbans);
        }



        console.log(`✅ Automatically unbanned user ${userId} from guild ${guildId}`);

    } catch (error) {
        console.error(`Error automatically unbanning user ${userId} from guild ${guildId}:`, error);
    }
}

// Initialize tempban checker on bot startup
module.exports.initTempbanChecker = (client) => {
    const tempbans = loadTempbans();
    const now = Date.now();
    
    for (const [guildId, guildTempbans] of Object.entries(tempbans)) {
        for (const [userId, tempban] of Object.entries(guildTempbans)) {
            if (tempban.unbanAt <= now) {
                // Should have been unbanned already
                performUnban(client, guildId, userId);
            } else {
                // Schedule future unban
                scheduleUnban(client, guildId, userId, tempban.unbanAt);
            }
        }
    }
    
    console.log('✅ Tempban checker initialized');
};
