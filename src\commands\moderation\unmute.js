const { SlashCommandBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('unmute')
        .setDescription('Unmute a user in a voice channel.')
        .addUserOption(option => 
            option.setName('user')
                .setDescription('The user to unmute')
                .setRequired(true)),
    async execute(interaction) {
        if (!interaction.member.permissions.has('ADMINISTRATOR')) {
            return interaction.reply({ content: "You don't have permission to use this command.", ephemeral: true });
        }

        const user = interaction.options.getMember('user');
        if (!user.voice.channel) {
            return interaction.reply({ content: "User is not in a voice channel.", ephemeral: true });
        }

        await user.voice.setMute(false);
        interaction.reply({ content: `${user} has been unmuted.`, ephemeral: true });
    }
};
