const { SlashCommandBuilder, PermissionsBitField } = require("discord.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("timeout")
    .setDescription("Timeout a user for a specified duration.")
    .addUserOption((option) =>
      option
        .setName("target")
        .setDescription("The user to timeout")
        .setRequired(true)
    )
    .addIntegerOption((option) =>
      option
        .setName("duration")
        .setDescription("Duration of the timeout in minutes")
        .setRequired(true)
    )
    .addStringOption((option) =>
      option
        .setName("reason")
        .setDescription("Reason for the timeout")
    ),

  async execute(interaction) {
    // Check if the user has the "ModerateMembers" permission
    if (!interaction.member.permissions.has(PermissionsBitField.Flags.ModerateMembers)) {
      return interaction.reply({
        content: "❌ You do not have permission to use this command.",
        ephemeral: true,
      });
    }

    // Check if the bot has the "ModerateMembers" permission
    if (!interaction.guild.members.me.permissions.has(PermissionsBitField.Flags.ModerateMembers)) {
      return interaction.reply({
        content: "❌ I do not have permission to timeout members.",
        ephemeral: true,
      });
    }

    const user = interaction.options.getUser("target");
    const duration = interaction.options.getInteger("duration");
    const reason = interaction.options.getString("reason") || "No reason provided.";

    // Fetch the member to timeout
    const member = interaction.guild.members.cache.get(user.id);

    if (!member) {
      return interaction.reply({
        content: "❌ The specified user is not in this server.",
        ephemeral: true,
      });
    }

    if (!member.moderatable) {
      return interaction.reply({
        content: "❌ I cannot timeout this member. Ensure my role is higher than theirs and I have the proper permissions.",
        ephemeral: true,
      });
    }

    // Validate the duration
    if (duration < 1 || duration > 40320) { // Max timeout duration is 28 days (40320 minutes)
      return interaction.reply({
        content: "❌ Please specify a duration between 1 and 40320 minutes (28 days).",
        ephemeral: true,
      });
    }

    try {
      // Apply the timeout
      const milliseconds = duration * 60 * 1000;
      await member.timeout(milliseconds, reason);

      await interaction.reply({
        content: `✅ ${user.tag} has been timed out for ${duration} minutes. Reason: ${reason}`,
        ephemeral: true, // Only the user who executed the command can see this message
      });
    } catch (error) {
      console.error(error);
      await interaction.reply({
        content: "❌ An error occurred while trying to timeout this member.",
        ephemeral: true,
      });
    }
  },
};