const { <PERSON><PERSON><PERSON><PERSON>mandB<PERSON>er, Embed<PERSON>uilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('lockdown')
        .setDescription('Lock or unlock the server/channel')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addStringOption(option =>
            option.setName('action')
                .setDescription('Lock or unlock')
                .setRequired(true)
                .addChoices(
                    { name: 'Lock Server', value: 'lock_server' },
                    { name: 'Unlock Server', value: 'unlock_server' },
                    { name: 'Lock Channel', value: 'lock_channel' },
                    { name: 'Unlock Channel', value: 'unlock_channel' }
                ))
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for the lockdown')
                .setRequired(false)
                .setMaxLength(200)),

    async execute(interaction) {
        const action = interaction.options.getString('action');
        const reason = interaction.options.getString('reason') || 'No reason provided';

        await interaction.deferReply();

        try {
            let embed;
            let affectedChannels = 0;

            switch (action) {
                case 'lock_server':
                    affectedChannels = await lockServer(interaction.guild, reason);
                    embed = new EmbedBuilder()
                        .setColor('#ff6b6b')
                        .setTitle('🔒 Server Locked')
                        .setDescription(`The server has been locked. Regular members cannot send messages in any channel.`)
                        .addFields(
                            { name: 'Moderator', value: `${interaction.user.tag}`, inline: true },
                            { name: 'Channels Affected', value: `${affectedChannels}`, inline: true },
                            { name: 'Reason', value: reason, inline: false }
                        )
                        .setTimestamp();
                    break;

                case 'unlock_server':
                    affectedChannels = await unlockServer(interaction.guild, reason);
                    embed = new EmbedBuilder()
                        .setColor('#4CAF50')
                        .setTitle('🔓 Server Unlocked')
                        .setDescription(`The server has been unlocked. Regular members can now send messages again.`)
                        .addFields(
                            { name: 'Moderator', value: `${interaction.user.tag}`, inline: true },
                            { name: 'Channels Affected', value: `${affectedChannels}`, inline: true },
                            { name: 'Reason', value: reason, inline: false }
                        )
                        .setTimestamp();
                    break;

                case 'lock_channel':
                    await lockChannel(interaction.channel, reason);
                    embed = new EmbedBuilder()
                        .setColor('#ff6b6b')
                        .setTitle('🔒 Channel Locked')
                        .setDescription(`${interaction.channel} has been locked. Regular members cannot send messages here.`)
                        .addFields(
                            { name: 'Moderator', value: `${interaction.user.tag}`, inline: true },
                            { name: 'Channel', value: `${interaction.channel.name}`, inline: true },
                            { name: 'Reason', value: reason, inline: false }
                        )
                        .setTimestamp();
                    break;

                case 'unlock_channel':
                    await unlockChannel(interaction.channel, reason);
                    embed = new EmbedBuilder()
                        .setColor('#4CAF50')
                        .setTitle('🔓 Channel Unlocked')
                        .setDescription(`${interaction.channel} has been unlocked. Regular members can now send messages here.`)
                        .addFields(
                            { name: 'Moderator', value: `${interaction.user.tag}`, inline: true },
                            { name: 'Channel', value: `${interaction.channel.name}`, inline: true },
                            { name: 'Reason', value: reason, inline: false }
                        )
                        .setTimestamp();
                    break;
            }



            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Error in lockdown command:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff6b6b')
                .setTitle('❌ Lockdown Failed')
                .setDescription('An error occurred while trying to perform the lockdown action.')
                .addFields(
                    { name: 'Error', value: error.message || 'Unknown error', inline: false }
                )
                .setTimestamp();

            await interaction.editReply({ embeds: [errorEmbed] });
        }
    }
};

// Lock the entire server
async function lockServer(guild, reason) {
    const everyoneRole = guild.roles.everyone;
    let affectedChannels = 0;

    // Get all text channels
    const textChannels = guild.channels.cache.filter(channel => 
        channel.type === 0 && // Text channels
        channel.permissionsFor(everyoneRole).has(PermissionFlagsBits.SendMessages)
    );

    // Lock each channel
    for (const [channelId, channel] of textChannels) {
        try {
            await channel.permissionOverwrites.edit(everyoneRole, {
                SendMessages: false
            }, { reason: `Server lockdown: ${reason}` });
            affectedChannels++;
        } catch (error) {
            console.error(`Failed to lock channel ${channel.name}:`, error);
        }
    }

    return affectedChannels;
}

// Unlock the entire server
async function unlockServer(guild, reason) {
    const everyoneRole = guild.roles.everyone;
    let affectedChannels = 0;

    // Get all text channels that are currently locked
    const textChannels = guild.channels.cache.filter(channel => 
        channel.type === 0 && // Text channels
        !channel.permissionsFor(everyoneRole).has(PermissionFlagsBits.SendMessages)
    );

    // Unlock each channel
    for (const [channelId, channel] of textChannels) {
        try {
            // Remove the SendMessages override to restore default permissions
            const overwrite = channel.permissionOverwrites.cache.get(everyoneRole.id);
            if (overwrite && overwrite.deny.has(PermissionFlagsBits.SendMessages)) {
                await channel.permissionOverwrites.edit(everyoneRole, {
                    SendMessages: null
                }, { reason: `Server unlock: ${reason}` });
                affectedChannels++;
            }
        } catch (error) {
            console.error(`Failed to unlock channel ${channel.name}:`, error);
        }
    }

    return affectedChannels;
}

// Lock a specific channel
async function lockChannel(channel, reason) {
    const everyoneRole = channel.guild.roles.everyone;
    
    await channel.permissionOverwrites.edit(everyoneRole, {
        SendMessages: false
    }, { reason: `Channel lockdown: ${reason}` });
}

// Unlock a specific channel
async function unlockChannel(channel, reason) {
    const everyoneRole = channel.guild.roles.everyone;
    
    // Remove the SendMessages override to restore default permissions
    await channel.permissionOverwrites.edit(everyoneRole, {
        SendMessages: null
    }, { reason: `Channel unlock: ${reason}` });
}
