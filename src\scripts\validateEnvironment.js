const fs = require('fs');
const path = require('path');

/**
 * Environment Validation and Setup Helper
 * This script validates your Discord bot environment configuration
 * and provides helpful guidance for fixing issues.
 */

class EnvironmentValidator {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.envPath = path.join(process.cwd(), '.env');
        this.envVars = {};
    }

    // Load and parse .env file
    loadEnvironment() {
        try {
            if (!fs.existsSync(this.envPath)) {
                this.errors.push('❌ .env file not found in project root');
                return false;
            }

            const envContent = fs.readFileSync(this.envPath, 'utf8');
            const lines = envContent.split('\n');

            for (const line of lines) {
                const trimmed = line.trim();
                if (trimmed && !trimmed.startsWith('#')) {
                    const [key, ...valueParts] = trimmed.split('=');
                    if (key && valueParts.length > 0) {
                        this.envVars[key.trim()] = valueParts.join('=').trim();
                    }
                }
            }

            console.log('✅ .env file loaded successfully');
            return true;
        } catch (error) {
            this.errors.push(`❌ Error reading .env file: ${error.message}`);
            return false;
        }
    }

    // Validate Discord bot token
    validateToken() {
        const token = this.envVars.TOKEN;
        
        if (!token) {
            this.errors.push('❌ TOKEN is missing from .env file');
            return false;
        }

        if (token.length < 50) {
            this.errors.push('❌ TOKEN appears to be invalid (too short)');
            return false;
        }

        // Basic Discord token format validation
        const tokenParts = token.split('.');
        if (tokenParts.length !== 3) {
            this.errors.push('❌ TOKEN format appears invalid (should have 3 parts separated by dots)');
            return false;
        }

        console.log('✅ TOKEN format appears valid');
        return true;
    }

    // Validate Discord client ID
    validateClientId() {
        const clientId = this.envVars.DISCORD_CLIENT_ID;
        
        if (!clientId) {
            this.errors.push('❌ DISCORD_CLIENT_ID is missing from .env file');
            return false;
        }

        // Discord IDs are 17-19 digit numbers
        if (!/^\d{17,19}$/.test(clientId)) {
            this.errors.push('❌ DISCORD_CLIENT_ID format appears invalid (should be 17-19 digits)');
            return false;
        }

        console.log(`✅ DISCORD_CLIENT_ID appears valid: ${clientId}`);
        return true;
    }

    // Validate guild IDs (optional)
    validateGuildIds() {
        const guildIds = this.envVars.DISCORD_GUILD_IDS;
        
        if (!guildIds) {
            this.warnings.push('⚠️ DISCORD_GUILD_IDS not set - commands will be registered globally (takes up to 1 hour to update)');
            return true;
        }

        const ids = guildIds.split(',').map(id => id.trim()).filter(id => id);
        
        if (ids.length === 0) {
            this.warnings.push('⚠️ DISCORD_GUILD_IDS is empty - commands will be registered globally');
            return true;
        }

        let validIds = 0;
        for (const id of ids) {
            if (!/^\d{17,19}$/.test(id)) {
                this.errors.push(`❌ Invalid guild ID format: ${id} (should be 17-19 digits)`);
            } else {
                validIds++;
            }
        }

        if (validIds > 0) {
            console.log(`✅ ${validIds} valid guild ID(s) found`);
            if (validIds !== ids.length) {
                this.warnings.push(`⚠️ ${ids.length - validIds} invalid guild ID(s) will be ignored`);
            }
        }

        return validIds > 0;
    }

    // Generate OAuth2 invite URL
    generateInviteUrl() {
        const clientId = this.envVars.DISCORD_CLIENT_ID;
        
        if (!clientId) {
            return null;
        }

        // Permissions for all bot features
        const permissions = [
            'Send Messages',
            'Use Slash Commands', 
            'Read Message History',
            'Manage Messages',
            'Manage Roles',
            'Kick Members',
            'Ban Members',
            'Manage Channels',
            'Connect',
            'Speak',
            'Move Members'
        ];

        // Permission value for comprehensive bot permissions
        const permissionValue = '8'; // Administrator (for simplicity)
        
        const url = `https://discord.com/api/oauth2/authorize?client_id=${clientId}&permissions=${permissionValue}&scope=bot%20applications.commands`;
        
        return {
            url,
            permissions,
            note: 'This URL includes Administrator permissions for simplicity. For production, use specific permissions.'
        };
    }

    // Run all validations
    async validate() {
        console.log('🔍 Validating Discord bot environment...\n');

        // Load environment
        if (!this.loadEnvironment()) {
            this.printResults();
            return false;
        }

        // Run validations
        const tokenValid = this.validateToken();
        const clientIdValid = this.validateClientId();
        const guildIdsValid = this.validateGuildIds();

        // Print results
        this.printResults();

        // Generate invite URL if basic validation passes
        if (tokenValid && clientIdValid) {
            this.printInviteUrl();
        }

        return this.errors.length === 0;
    }

    // Print validation results
    printResults() {
        console.log('\n' + '='.repeat(60));
        console.log('📊 VALIDATION RESULTS');
        console.log('='.repeat(60));

        if (this.errors.length === 0) {
            console.log('✅ All validations passed!');
        } else {
            console.log(`❌ ${this.errors.length} error(s) found:`);
            this.errors.forEach(error => console.log(`   ${error}`));
        }

        if (this.warnings.length > 0) {
            console.log(`\n⚠️ ${this.warnings.length} warning(s):`);
            this.warnings.forEach(warning => console.log(`   ${warning}`));
        }

        console.log('='.repeat(60));
    }

    // Print OAuth2 invite URL
    printInviteUrl() {
        const inviteData = this.generateInviteUrl();
        
        if (!inviteData) {
            return;
        }

        console.log('\n🔗 DISCORD BOT INVITE URL');
        console.log('='.repeat(60));
        console.log('Copy this URL to invite your bot to servers:');
        console.log(`\n${inviteData.url}\n`);
        console.log('Required Scopes: bot, applications.commands');
        console.log('Permissions included:');
        inviteData.permissions.forEach(perm => console.log(`   ✅ ${perm}`));
        console.log(`\n💡 ${inviteData.note}`);
        console.log('='.repeat(60));
    }

    // Create a sample .env file
    static createSampleEnv() {
        const sampleContent = `# Discord Bot Configuration
# Get these values from Discord Developer Portal

# Bot Token (from Bot section)
TOKEN=your_bot_token_here

# Application ID (from General Information)
DISCORD_CLIENT_ID=1385134774200307722

# Guild IDs for instant command updates (optional, comma-separated)
# Leave empty for global commands (takes up to 1 hour to update)
DISCORD_GUILD_IDS=1272745870722601051,1190859480137732256

# Optional: Set to 'development' for additional logging
NODE_ENV=development
`;

        const envPath = path.join(process.cwd(), '.env.example');
        
        try {
            fs.writeFileSync(envPath, sampleContent);
            console.log('✅ Created .env.example file with sample configuration');
            console.log('📝 Copy this to .env and fill in your actual values');
        } catch (error) {
            console.error('❌ Failed to create .env.example:', error.message);
        }
    }
}

// CLI interface
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--create-sample')) {
        EnvironmentValidator.createSampleEnv();
    } else {
        const validator = new EnvironmentValidator();
        validator.validate().then(success => {
            if (!success) {
                console.log('\n💡 Run with --create-sample to generate a sample .env file');
                process.exit(1);
            }
        });
    }
}

module.exports = EnvironmentValidator;
