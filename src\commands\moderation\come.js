const { SlashCommandBuilder } = require("@discordjs/builders");

module.exports = {
    data: new SlashCommandBuilder()
        .setName("come")
        .setDescription("Call the member for come.")
        .addUserOption(option => 
            option
                .setName("user")
                .setDescription("The user to call him.")
                .setRequired(true)
        ),
    async execute(interaction) {
        const users = interaction.options.getUser("user");
        const ticketChannelId = "1316945742241333256"; // Replace with your ticket channel ID

        await interaction.reply({ content: `تم استدعاء ${users} بنجاح` });
        users.send({ content: `**
${users} ⚠️ تم استدعائك من قبل الإدارة
        
يرجى فتح تذكرة في <#${ticketChannelId}> للتواصل مع الإدارة

Called by: ${interaction.user}**` });
    },
};

