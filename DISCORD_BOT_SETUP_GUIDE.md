# Discord Bot Setup Guide

## 🚀 Complete Setup Instructions

This guide will help you fix the `DiscordAPIError[50001]: Missing Access` error and ensure all your commands are properly registered with Discord.

## 📋 Prerequisites

- Discord account
- Node.js installed
- Your bot's source code

## 🔧 Step 1: Discord Developer Portal Setup

### 1.1 Access the Developer Portal
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Log in with your Discord account
3. Find your application with ID: `1385134774200307722`

### 1.2 Configure Bot Settings
1. Click on your application
2. Go to the **"Bot"** section in the left sidebar
3. Ensure the following settings:
   - ✅ **Public Bot**: Enabled (if you want others to invite it)
   - ✅ **Requires OAuth2 Code Grant**: Disabled
   - ✅ **Bot Permissions**: Configure as needed

### 1.3 Required Bot Permissions
In the **"Bot"** section, ensure your bot has these permissions:
- ✅ **Send Messages**
- ✅ **Use Slash Commands**
- ✅ **Read Message History**
- ✅ **Manage Messages** (for moderation commands)
- ✅ **Manage Roles** (for role commands)
- ✅ **Kick Members** (for kick command)
- ✅ **Ban Members** (for ban commands)
- ✅ **Manage Channels** (for channel management)
- ✅ **Connect** (for voice commands)
- ✅ **Speak** (for voice commands)
- ✅ **Move Members** (for voice management)

## 🔑 Step 2: OAuth2 Configuration

### 2.1 Set OAuth2 Scopes
1. Go to **"OAuth2"** → **"URL Generator"** in the left sidebar
2. Select the following scopes:
   - ✅ **`bot`** - Required for basic bot functionality
   - ✅ **`applications.commands`** - **CRITICAL** for slash commands

### 2.2 Set Bot Permissions
After selecting scopes, choose these permissions:
- ✅ **Administrator** (easiest, but not recommended for production)
- OR select specific permissions:
  - ✅ **Send Messages**
  - ✅ **Use Slash Commands**
  - ✅ **Manage Messages**
  - ✅ **Manage Roles**
  - ✅ **Kick Members**
  - ✅ **Ban Members**
  - ✅ **Manage Channels**
  - ✅ **Connect**
  - ✅ **Speak**
  - ✅ **Move Members**

### 2.3 Generate Invite URL
1. Copy the generated URL at the bottom
2. **Example URL format:**
```
https://discord.com/api/oauth2/authorize?client_id=1385134774200307722&permissions=8&scope=bot%20applications.commands
```

## 🔗 Step 3: Invite Bot to Servers

### 3.1 Invite to Your Servers
1. Use the generated OAuth2 URL
2. Select the servers where you want to add the bot
3. **IMPORTANT**: Make sure you have "Manage Server" permission in those servers
4. Authorize the bot with all requested permissions

### 3.2 Verify Bot Presence
1. Check that the bot appears in your server's member list
2. Verify the bot has the correct roles and permissions
3. The bot should have a "Bot" tag next to its name

## 📁 Step 4: Environment Configuration

### 4.1 Update .env File
Create or update your `.env` file with these variables:

```env
# Bot Token (from Discord Developer Portal → Bot → Token)
TOKEN=your_bot_token_here

# Application ID (from Discord Developer Portal → General Information)
DISCORD_CLIENT_ID=1385134774200307722

# Guild IDs (comma-separated, optional - leave empty for global commands)
DISCORD_GUILD_IDS=1272745870722601051,1190859480137732256
```

### 4.2 Environment Variables Explained

- **`TOKEN`**: Your bot's secret token
  - Found in: Developer Portal → Your App → Bot → Token
  - **NEVER SHARE THIS TOKEN**

- **`DISCORD_CLIENT_ID`**: Your application's ID
  - Found in: Developer Portal → Your App → General Information → Application ID
  - This should be: `1385134774200307722`

- **`DISCORD_GUILD_IDS`**: Server IDs where you want guild-specific commands
  - Optional: Leave empty for global commands (takes up to 1 hour to update)
  - Guild commands update instantly but only work in specified servers
  - Get server ID: Right-click server name → Copy Server ID (Developer Mode required)

## 🚨 Step 5: Troubleshooting Common Issues

### Issue 1: "Missing Access" Error
**Cause**: Bot lacks `applications.commands` scope
**Solution**: 
1. Re-invite bot with correct OAuth2 URL including `applications.commands` scope
2. Make sure you have "Manage Server" permission when inviting

### Issue 2: Commands Not Appearing
**Possible Causes & Solutions**:
1. **Guild vs Global Commands**:
   - Guild commands: Update instantly, only work in specified servers
   - Global commands: Work everywhere, take up to 1 hour to update
   
2. **Permission Issues**:
   - Re-invite bot with proper permissions
   - Check bot's role hierarchy in server settings

3. **Cache Issues**:
   - Restart Discord client
   - Try in incognito/private browser
   - Wait up to 1 hour for global commands

### Issue 3: Bot Connects But Commands Fail
**Solutions**:
1. Verify all environment variables are correct
2. Check bot has necessary permissions in the server
3. Ensure bot's role is high enough in the hierarchy
4. Check server's slash command permissions

## ✅ Step 6: Verification

### 6.1 Test Bot Functionality
1. Start your bot: `npm run dev`
2. Check console for successful command registration
3. In Discord, type `/` to see if your commands appear
4. Test a simple command like `/ping`

### 6.2 Expected Console Output
```
✅ Command Loaded: ping (tools)
✅ Command Loaded: help (utility)
... (all 34 commands)
📊 Total commands loaded locally: 34
🔍 Validating bot permissions...
✅ Bot application validated: YourBotName
✅ Commands registered successfully for 2/2 guilds.
✅ Logged in as YourBot#1234!
```

## 🔄 Step 7: Switching Between Guild and Global Commands

### Guild Commands (Recommended for Development)
- **Pros**: Update instantly, easier testing
- **Cons**: Only work in specified servers
- **Setup**: Add server IDs to `DISCORD_GUILD_IDS`

### Global Commands (Recommended for Production)
- **Pros**: Work in all servers where bot is present
- **Cons**: Take up to 1 hour to update
- **Setup**: Leave `DISCORD_GUILD_IDS` empty or remove the variable

## 📞 Need Help?

If you're still experiencing issues:
1. Check the console output for specific error messages
2. Verify your `.env` file has the correct values
3. Make sure you're using the OAuth2 URL with `applications.commands` scope
4. Try removing and re-adding the bot to your server

## 🎯 Quick Fix Checklist

- [ ] Bot has `applications.commands` scope
- [ ] Bot was invited with proper permissions
- [ ] `.env` file has correct `TOKEN` and `DISCORD_CLIENT_ID`
- [ ] Guild IDs are correct (if using guild commands)
- [ ] Bot has necessary permissions in the server
- [ ] You have "Manage Server" permission when inviting the bot
