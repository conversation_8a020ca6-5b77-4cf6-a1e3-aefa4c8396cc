const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');
const fs = require('fs');
const path = require('path');

const WARNINGS_FILE = path.join(__dirname, '../../data/warnings.json');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('warn')
        .setDescription('Warn a user for breaking rules')
        .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers)
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The user to warn')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for the warning')
                .setRequired(true)
                .setMaxLength(500))
        .addBooleanOption(option =>
            option.setName('dm')
                .setDescription('Send a DM to the user about the warning')
                .setRequired(false)),

    async execute(interaction) {
        const user = interaction.options.getUser('user');
        const reason = interaction.options.getString('reason');
        const sendDM = interaction.options.getBoolean('dm') ?? true;

        // Check if user is trying to warn themselves
        if (user.id === interaction.user.id) {
            return interaction.reply({
                content: '❌ You cannot warn yourself!',
                ephemeral: true
            });
        }

        // Check if user is trying to warn a bot
        if (user.bot) {
            return interaction.reply({
                content: '❌ You cannot warn bots!',
                ephemeral: true
            });
        }

        // Check if user is trying to warn someone with higher or equal permissions
        const member = interaction.guild.members.cache.get(user.id);
        if (member && member.roles.highest.position >= interaction.member.roles.highest.position) {
            return interaction.reply({
                content: '❌ You cannot warn someone with equal or higher permissions!',
                ephemeral: true
            });
        }

        try {
            // Load existing warnings
            const warnings = loadWarnings();
            const guildId = interaction.guild.id;
            const userId = user.id;

            if (!warnings[guildId]) {
                warnings[guildId] = {};
            }
            if (!warnings[guildId][userId]) {
                warnings[guildId][userId] = [];
            }

            // Create warning object
            const warning = {
                id: generateWarningId(),
                reason: reason,
                moderator: interaction.user.id,
                timestamp: Date.now(),
                guildId: guildId
            };

            // Add warning to user's record
            warnings[guildId][userId].push(warning);

            // Save warnings
            if (!saveWarnings(warnings)) {
                return interaction.reply({
                    content: '❌ Failed to save warning. Please try again.',
                    ephemeral: true
                });
            }

            // Get warning count
            const warningCount = warnings[guildId][userId].length;

            // Create embed for the warning
            const embed = new EmbedBuilder()
                .setColor('#FFA500')
                .setTitle('⚠️ User Warned')
                .addFields(
                    { name: 'User', value: `${user.tag} (${user.id})`, inline: true },
                    { name: 'Moderator', value: `${interaction.user.tag}`, inline: true },
                    { name: 'Warning Count', value: `${warningCount}`, inline: true },
                    { name: 'Reason', value: reason, inline: false },
                    { name: 'Warning ID', value: warning.id, inline: true }
                )
                .setThumbnail(user.displayAvatarURL({ dynamic: true }))
                .setTimestamp();

            // Send DM to user if requested
            if (sendDM) {
                try {
                    const dmEmbed = new EmbedBuilder()
                        .setColor('#FFA500')
                        .setTitle(`⚠️ Warning from ${interaction.guild.name}`)
                        .addFields(
                            { name: 'Reason', value: reason, inline: false },
                            { name: 'Moderator', value: interaction.user.tag, inline: true },
                            { name: 'Total Warnings', value: `${warningCount}`, inline: true }
                        )
                        .setFooter({ text: 'Please follow the server rules to avoid further warnings.' })
                        .setTimestamp();

                    await user.send({ embeds: [dmEmbed] });
                    embed.setFooter({ text: '✅ User has been notified via DM' });
                } catch (error) {
                    embed.setFooter({ text: '⚠️ Could not send DM to user' });
                }
            }



            // Auto-actions based on warning count
            let autoAction = '';
            if (warningCount >= 5) {
                // Auto-ban after 5 warnings
                try {
                    await member.ban({ reason: `Auto-ban: ${warningCount} warnings` });
                    autoAction = '\n🔨 **Auto-banned** for reaching 5 warnings!';
                } catch (error) {
                    autoAction = '\n⚠️ Failed to auto-ban user';
                }
            } else if (warningCount >= 3) {
                // Auto-timeout after 3 warnings
                try {
                    await member.timeout(24 * 60 * 60 * 1000, `Auto-timeout: ${warningCount} warnings`); // 24 hours
                    autoAction = '\n⏰ **Auto-timed out** for 24 hours (3+ warnings)';
                } catch (error) {
                    autoAction = '\n⚠️ Failed to auto-timeout user';
                }
            }

            if (autoAction) {
                embed.addFields({ name: 'Auto-Action', value: autoAction, inline: false });
            }

            await interaction.reply({ embeds: [embed] });

        } catch (error) {
            console.error('Error in warn command:', error);
            await interaction.reply({
                content: '❌ An error occurred while warning the user.',
                ephemeral: true
            });
        }
    }
};

// Load warnings from JSON file
function loadWarnings() {
    try {
        if (!fs.existsSync(WARNINGS_FILE)) {
            return {};
        }
        const data = fs.readFileSync(WARNINGS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading warnings:', error);
        return {};
    }
}

// Save warnings to JSON file
function saveWarnings(warnings) {
    try {
        const dataDir = path.dirname(WARNINGS_FILE);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }
        
        fs.writeFileSync(WARNINGS_FILE, JSON.stringify(warnings, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving warnings:', error);
        return false;
    }
}

// Generate unique warning ID
function generateWarningId() {
    return 'W' + Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
}
