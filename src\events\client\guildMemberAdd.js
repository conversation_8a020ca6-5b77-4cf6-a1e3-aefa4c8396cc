const { EmbedBuilder } = require('discord.js');

module.exports = {
  name: 'guildMemberAdd', // Event name
  once: false, // Set to false because this event can trigger multiple times
  execute(member, client) {
    const welcomeChannelId = '1272745870982647901'; // Replace with your channel ID
    const welcomeChannel = member.guild.channels.cache.get(welcomeChannelId);

    if (!welcomeChannel) {
      console.error('Welcome channel not found!');
      return;
    }

    // Create the embed
    const welcomeEmbed = new EmbedBuilder()
      .setColor('#61607e') // Dark red color
      .setTitle(`Welcome ${member.user.username}!`) // Use the member's username
      .setDescription(
        `**Say Hi in** <#1272745871196426254>\n` +
        `**Check Rules in** <#1272745870982647902>\n` +
        `**Support in** <#1316945742241333256>`
      )
      .setThumbnail(member.user.displayAvatarURL({ dynamic: true })) // User's avatar as thumbnail
      .setImage('https://cdn.discordapp.com/banners/1272745870722601051/a_9c4940ba902913b9972894b6989f674e.gif?size=300&quot') // Your custom GIF
      .setFooter({ text: '.gg/sahm', iconURL: member.guild.iconURL({ dynamic: true }) }) // Footer with timestamp
      .setTimestamp();

    // Send the embed to the welcome channel
    welcomeChannel.send({ content: `Welcome <@${member.user.id}>!`, embeds: [welcomeEmbed] });
  },
};