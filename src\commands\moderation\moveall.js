const { SlashCommandBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('moveall')
        .setDescription('Move all users from one voice channel to another.')
        .addChannelOption(option => 
            option.setName('from')
                .setDescription('The source voice channel')
                .setRequired(true))
        .addChannelOption(option => 
            option.setName('to')
                .setDescription('The target voice channel')
                .setRequired(true)),
    async execute(interaction) {
        if (!interaction.member.permissions.has('ADMINISTRATOR')) {
            return interaction.reply({ content: "You don't have permission to use this command.", ephemeral: true });
        }

        const fromChannel = interaction.options.getChannel('from');
        const toChannel = interaction.options.getChannel('to');

        if (!fromChannel.isVoice() || !toChannel.isVoice()) {
            return interaction.reply({ content: "Both channels must be voice channels.", ephemeral: true });
        }

        for (const member of fromChannel.members.values()) {
            await member.voice.setChannel(toChannel);
        }

        interaction.reply({ content: `All users from ${fromChannel} have been moved to ${toChannel}.`, ephemeral: false });
    }
};
