
const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('info')
        .setDescription('Display server information panel'),

    async execute(interaction) {
        // First defer the reply ephemerally
        await interaction.deferReply({ ephemeral: true });

        // Get server banner
        const imageUrl = 'https://cdn.discordapp.com/banners/1272745870722601051/a_9c4940ba902913b9972894b6989f674e.gif?size=300&quot';

        // Create the embed with the original layout
        const initialEmbed = new EmbedBuilder()
            .setColor('#0f0f10') // Dark background
            .setAuthor({
                name: 'SAHM community',
                iconURL: 'https://cdn.discordapp.com/icons/1272745870722601051/a_afc91405fdc3ba5a738bf5ef72ed7ca9.gif?size=128'
            })
            .setDescription(`**شكر لانضمامك في مجتمع SAHM يمكنك مشاهدة جميع القوانين والأنظمة عن طريق الأزرارالموجودة ب الأسفل و لطلب الدعم الفنييمكنك الذهاب لروم** <#1316945742241333256>`)
            .setImage(imageUrl) // Banner image at the bottom
            .setFooter({
                text: 'Bot by SH startup'
            });

        // First row of buttons
        const row1 = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
                .setCustomId('server_info')
                .setLabel('Server Info')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('ℹ️'),
            new ButtonBuilder()
                .setCustomId('rules')
                .setLabel('Rules')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('📜'),
            new ButtonBuilder()
                .setCustomId('Level_System')
                .setLabel('Level System')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('<:discotoolsxyzicon10:1350916763218481214>')
                .setDisabled(true)  // This makes the button appear but not clickable
        );

        // Second row of buttons
        const row2 = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
                .setCustomId('support')
                .setLabel('Support')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('<:discotoolsxyzicon11:1350918123737317439>'),
            new ButtonBuilder()
                .setCustomId('social_media')
                .setLabel('Social Media')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('🌐'),
            new ButtonBuilder()
                .setCustomId('server_map')
                .setLabel('📌 Server Map')
                .setStyle(ButtonStyle.Secondary)
        );

        // Send the panel with both rows
        await interaction.channel.send({
            embeds: [initialEmbed],
            components: [row1, row2]
        });

        // Delete the original slash command response
        await interaction.deleteReply();
    }
};
