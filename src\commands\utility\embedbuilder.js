const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, ChannelType } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('embedbuilder')
        .setDescription('Create custom embeds with a dropdown channel selector (better for special characters)'),

    async execute(interaction) {
        // Get all text channels the bot can send messages to
        const textChannels = interaction.guild.channels.cache
            .filter(channel => 
                channel.type === ChannelType.GuildText && 
                channel.permissionsFor(interaction.client.user).has('SendMessages')
            )
            .sort((a, b) => a.position - b.position);

        if (textChannels.size === 0) {
            return interaction.reply({
                content: 'I don\'t have permission to send messages in any text channels.',
                ephemeral: true
            });
        }

        // Create channel selection dropdown
        const channelSelect = new StringSelectMenuBuilder()
            .setCustomId('embedbuilder_select_channel')
            .setPlaceholder('Select a channel to send the embed to');

        // Add channels to dropdown (limit to 25 due to Discord restrictions)
        const channelsArray = [...textChannels.values()].slice(0, 25);
        
        channelsArray.forEach(channel => {
            // Clean channel name for display
            let displayName = channel.name;
            
            // Check if name has special characters
            const hasSpecialChars = /[^\x00-\x7F]/.test(displayName);
            if (hasSpecialChars) {
                displayName = `${displayName} 🌐`;
            }
            
            // Truncate if too long
            if (displayName.length > 100) {
                displayName = displayName.substring(0, 97) + '...';
            }

            channelSelect.addOptions({
                label: displayName,
                value: channel.id,
                description: `ID: ${channel.id}`,
                emoji: '📝'
            });
        });

        const selectRow = new ActionRowBuilder().addComponents(channelSelect);

        // Create initial embed
        const initialEmbed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🛠️ Embed Builder')
            .setDescription('Select a channel below to start creating your embed.\n\n🌐 = Channel name contains special characters')
            .addFields(
                { name: '📋 Instructions', value: '1. Select a channel from the dropdown\n2. Customize your embed with buttons\n3. Send when ready!', inline: false }
            )
            .setFooter({ text: 'Embed Builder - Step 1/2' })
            .setTimestamp();

        await interaction.reply({
            embeds: [initialEmbed],
            components: [selectRow],
            ephemeral: true
        });
    }
};

// Helper function to create embed builder components
function createEmbedBuilderComponents() {
    const row1 = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId('embed_title')
            .setLabel('Set Title')
            .setStyle(ButtonStyle.Primary),
        new ButtonBuilder()
            .setCustomId('embed_description')
            .setLabel('Set Description')
            .setStyle(ButtonStyle.Primary),
        new ButtonBuilder()
            .setCustomId('embed_color')
            .setLabel('Set Color')
            .setStyle(ButtonStyle.Primary),
        new ButtonBuilder()
            .setCustomId('embed_footer')
            .setLabel('Set Footer')
            .setStyle(ButtonStyle.Primary)
    );

    const row2 = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId('embed_image')
            .setLabel('Set Image')
            .setStyle(ButtonStyle.Secondary),
        new ButtonBuilder()
            .setCustomId('embed_thumbnail')
            .setLabel('Set Thumbnail')
            .setStyle(ButtonStyle.Secondary),
        new ButtonBuilder()
            .setCustomId('embed_author')
            .setLabel('Set Author')
            .setStyle(ButtonStyle.Secondary),
        new ButtonBuilder()
            .setCustomId('embed_timestamp')
            .setLabel('Toggle Timestamp')
            .setStyle(ButtonStyle.Secondary)
    );

    const row3 = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId('embed_field')
            .setLabel('Add Field')
            .setStyle(ButtonStyle.Success),
        new ButtonBuilder()
            .setCustomId('embed_remove_field')
            .setLabel('Remove Field')
            .setStyle(ButtonStyle.Danger),
        new ButtonBuilder()
            .setCustomId('embed_preview')
            .setLabel('Preview')
            .setStyle(ButtonStyle.Secondary),
        new ButtonBuilder()
            .setCustomId('embed_send')
            .setLabel('Send Embed')
            .setStyle(ButtonStyle.Success)
    );

    return [row1, row2, row3];
}

// Export the helper function for use in interaction handler
module.exports.createEmbedBuilderComponents = createEmbedBuilderComponents;
