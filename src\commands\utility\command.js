const { <PERSON><PERSON><PERSON>ommand<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle, StringSelectMenuBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');

const CUSTOM_COMMANDS_FILE = path.join(__dirname, '../../data/customCommands.json');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('command')
        .setDescription('Manage custom commands')
        .addSubcommand(subcommand =>
            subcommand
                .setName('create')
                .setDescription('Create a new custom command'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List all custom commands'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('edit')
                .setDescription('Edit an existing custom command')
                .addStringOption(option =>
                    option.setName('command_name')
                        .setDescription('The name of the command to edit')
                        .setRequired(true)
                        .setAutocomplete(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('delete')
                .setDescription('Delete an existing custom command')
                .addStringOption(option =>
                    option.setName('command_name')
                        .setDescription('The name of the command to delete')
                        .setRequired(true)
                        .setAutocomplete(true))),

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();

        switch (subcommand) {
            case 'create':
                await handleCreateCommand(interaction);
                break;
            case 'list':
                await handleListCommands(interaction);
                break;
            case 'edit':
                await handleEditCommand(interaction);
                break;
            case 'delete':
                await handleDeleteCommand(interaction);
                break;
        }
    },

    async autocomplete(interaction) {
        const focusedOption = interaction.options.getFocused(true);

        if (focusedOption.name === 'command_name') {
            const customCommands = loadCustomCommands();
            const choices = Object.keys(customCommands)
                .filter(name => name.toLowerCase().includes(focusedOption.value.toLowerCase()))
                .slice(0, 25)
                .map(name => ({ name, value: name }));

            await interaction.respond(choices);
        }
    }
};

// Load custom commands from JSON file
function loadCustomCommands() {
    try {
        if (!fs.existsSync(CUSTOM_COMMANDS_FILE)) {
            return {};
        }
        const data = fs.readFileSync(CUSTOM_COMMANDS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading custom commands:', error);
        return {};
    }
}

// Save custom commands to JSON file
function saveCustomCommands(commands) {
    try {
        // Ensure the data directory exists
        const dataDir = path.dirname(CUSTOM_COMMANDS_FILE);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        fs.writeFileSync(CUSTOM_COMMANDS_FILE, JSON.stringify(commands, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving custom commands:', error);
        return false;
    }
}

// Handle create command
async function handleCreateCommand(interaction) {
    const modal = new ModalBuilder()
        .setCustomId('create_command_modal')
        .setTitle('Create Custom Command');

    const nameInput = new TextInputBuilder()
        .setCustomId('command_name')
        .setLabel('Command Name')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Enter command name (without /)')
        .setRequired(true)
        .setMaxLength(32);

    const descriptionInput = new TextInputBuilder()
        .setCustomId('command_description')
        .setLabel('Command Description')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Enter command description')
        .setRequired(true)
        .setMaxLength(100);

    const responseInput = new TextInputBuilder()
        .setCustomId('command_response')
        .setLabel('Command Response')
        .setStyle(TextInputStyle.Paragraph)
        .setPlaceholder('Enter the response message')
        .setRequired(true)
        .setMaxLength(2000);

    const nameRow = new ActionRowBuilder().addComponents(nameInput);
    const descRow = new ActionRowBuilder().addComponents(descriptionInput);
    const responseRow = new ActionRowBuilder().addComponents(responseInput);

    modal.addComponents(nameRow, descRow, responseRow);

    await interaction.showModal(modal);
}

// Handle list commands
async function handleListCommands(interaction) {
    const customCommands = loadCustomCommands();
    const commandNames = Object.keys(customCommands);

    if (commandNames.length === 0) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('📝 Custom Commands')
            .setDescription('No custom commands found. Use `/command create` to create your first custom command!')
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    // Pagination setup
    const itemsPerPage = 10;
    const totalPages = Math.ceil(commandNames.length / itemsPerPage);
    let currentPage = 0;

    const generateEmbed = (page) => {
        const start = page * itemsPerPage;
        const end = start + itemsPerPage;
        const pageCommands = commandNames.slice(start, end);

        const embed = new EmbedBuilder()
            .setColor('#4CAF50')
            .setTitle('📝 Custom Commands')
            .setDescription(`Showing ${pageCommands.length} of ${commandNames.length} custom commands`)
            .setFooter({ text: `Page ${page + 1} of ${totalPages}` })
            .setTimestamp();

        pageCommands.forEach(name => {
            const command = customCommands[name];
            embed.addFields({
                name: `/${name}`,
                value: `${command.description}\nCreated by: <@${command.createdBy}>\nCreated: <t:${Math.floor(command.createdAt / 1000)}:R>`,
                inline: false
            });
        });

        return embed;
    };

    const generateButtons = (page) => {
        const row = new ActionRowBuilder();

        if (totalPages > 1) {
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId('prev_page')
                    .setLabel('◀️ Previous')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(page === 0),
                new ButtonBuilder()
                    .setCustomId('next_page')
                    .setLabel('Next ▶️')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(page === totalPages - 1)
            );
        }

        return row.components.length > 0 ? [row] : [];
    };

    const embed = generateEmbed(currentPage);
    const components = generateButtons(currentPage);

    const response = await interaction.reply({
        embeds: [embed],
        components: components,
        ephemeral: true
    });

    if (totalPages > 1) {
        const collector = response.createMessageComponentCollector({
            time: 300000 // 5 minutes
        });

        collector.on('collect', async (i) => {
            if (i.user.id !== interaction.user.id) {
                return i.reply({ content: 'You cannot interact with this menu.', ephemeral: true });
            }

            if (i.customId === 'prev_page') {
                currentPage = Math.max(0, currentPage - 1);
            } else if (i.customId === 'next_page') {
                currentPage = Math.min(totalPages - 1, currentPage + 1);
            }

            const newEmbed = generateEmbed(currentPage);
            const newComponents = generateButtons(currentPage);

            await i.update({
                embeds: [newEmbed],
                components: newComponents
            });
        });

        collector.on('end', async () => {
            try {
                await response.edit({ components: [] });
            } catch (error) {
                // Message might have been deleted
            }
        });
    }
}

// Handle edit command
async function handleEditCommand(interaction) {
    const commandName = interaction.options.getString('command_name');
    const customCommands = loadCustomCommands();

    if (!customCommands[commandName]) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Command Not Found')
            .setDescription(`The command \`${commandName}\` does not exist.`)
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    const command = customCommands[commandName];

    // Check if user is the creator or has admin permissions
    if (command.createdBy !== interaction.user.id && !interaction.member.permissions.has('Administrator')) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Permission Denied')
            .setDescription('You can only edit commands you created, or you need Administrator permissions.')
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    const modal = new ModalBuilder()
        .setCustomId(`edit_command_modal_${commandName}`)
        .setTitle(`Edit Command: ${commandName}`);

    const descriptionInput = new TextInputBuilder()
        .setCustomId('command_description')
        .setLabel('Command Description')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Enter command description')
        .setValue(command.description)
        .setRequired(true)
        .setMaxLength(100);

    const responseInput = new TextInputBuilder()
        .setCustomId('command_response')
        .setLabel('Command Response')
        .setStyle(TextInputStyle.Paragraph)
        .setPlaceholder('Enter the response message')
        .setValue(command.response)
        .setRequired(true)
        .setMaxLength(2000);

    const descRow = new ActionRowBuilder().addComponents(descriptionInput);
    const responseRow = new ActionRowBuilder().addComponents(responseInput);

    modal.addComponents(descRow, responseRow);

    await interaction.showModal(modal);
}

// Handle delete command
async function handleDeleteCommand(interaction) {
    const commandName = interaction.options.getString('command_name');
    const customCommands = loadCustomCommands();

    if (!customCommands[commandName]) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Command Not Found')
            .setDescription(`The command \`${commandName}\` does not exist.`)
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    const command = customCommands[commandName];

    // Check if user is the creator or has admin permissions
    if (command.createdBy !== interaction.user.id && !interaction.member.permissions.has('Administrator')) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Permission Denied')
            .setDescription('You can only delete commands you created, or you need Administrator permissions.')
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    // Create confirmation buttons
    const confirmButton = new ButtonBuilder()
        .setCustomId(`confirm_delete_${commandName}`)
        .setLabel('Yes, Delete')
        .setStyle(ButtonStyle.Danger);

    const cancelButton = new ButtonBuilder()
        .setCustomId('cancel_delete')
        .setLabel('Cancel')
        .setStyle(ButtonStyle.Secondary);

    const row = new ActionRowBuilder().addComponents(confirmButton, cancelButton);

    const embed = new EmbedBuilder()
        .setColor('#ff9800')
        .setTitle('⚠️ Confirm Deletion')
        .setDescription(`Are you sure you want to delete the command \`/${commandName}\`?\n\n**This action cannot be undone!**`)
        .addFields({
            name: 'Command Details',
            value: `**Description:** ${command.description}\n**Response:** ${command.response.substring(0, 100)}${command.response.length > 100 ? '...' : ''}`,
            inline: false
        })
        .setTimestamp();

    const response = await interaction.reply({
        embeds: [embed],
        components: [row],
        ephemeral: true
    });

    // Create a collector for the confirmation
    const collector = response.createMessageComponentCollector({
        time: 30000 // 30 seconds
    });

    collector.on('collect', async (i) => {
        if (i.user.id !== interaction.user.id) {
            return i.reply({ content: 'You cannot interact with this confirmation.', ephemeral: true });
        }

        if (i.customId === `confirm_delete_${commandName}`) {
            // Delete the command
            delete customCommands[commandName];

            if (saveCustomCommands(customCommands)) {
                // Re-register commands with Discord
                if (interaction.client.handleCustomCommands) {
                    await interaction.client.handleCustomCommands.registerCustomCommands();
                }

                const successEmbed = new EmbedBuilder()
                    .setColor('#4CAF50')
                    .setTitle('✅ Command Deleted')
                    .setDescription(`The command \`/${commandName}\` has been successfully deleted.`)
                    .setTimestamp();

                await i.update({ embeds: [successEmbed], components: [] });
            } else {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#ff6b6b')
                    .setTitle('❌ Deletion Failed')
                    .setDescription('Failed to delete the custom command. Please try again.')
                    .setTimestamp();

                await i.update({ embeds: [errorEmbed], components: [] });
            }
        } else if (i.customId === 'cancel_delete') {
            const cancelEmbed = new EmbedBuilder()
                .setColor('#6c757d')
                .setTitle('❌ Deletion Cancelled')
                .setDescription('The command deletion has been cancelled.')
                .setTimestamp();

            await i.update({ embeds: [cancelEmbed], components: [] });
        }

        collector.stop();
    });

    collector.on('end', async (collected) => {
        if (collected.size === 0) {
            try {
                const timeoutEmbed = new EmbedBuilder()
                    .setColor('#6c757d')
                    .setTitle('⏰ Confirmation Timeout')
                    .setDescription('The deletion confirmation has timed out.')
                    .setTimestamp();

                await response.edit({ embeds: [timeoutEmbed], components: [] });
            } catch (error) {
                // Message might have been deleted
            }
        }
    });
}
