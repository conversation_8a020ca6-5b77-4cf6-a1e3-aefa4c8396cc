const { SlashCommandBuilder, PermissionsBitField } = require("discord.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("unban")
    .setDescription("Unbans a user from the server.")
    .addStringOption((option) =>
      option
        .setName("user")
        .setDescription("The ID or username#discriminator of the user to unban")
        .setRequired(true)
    )
    .addStringOption((option) =>
      option
        .setName("reason")
        .setDescription("The reason for unbanning the user")
    ),

  async execute(interaction) {
    // Check if the user has the "BanMembers" permission
    if (!interaction.member.permissions.has(PermissionsBitField.Flags.BanMembers)) {
      return interaction.reply({
        content: "❌ You do not have permission to use this command.",
        ephemeral: true,
      });
    }

    // Check if the bot has the "BanMembers" permission
    if (!interaction.guild.members.me.permissions.has(PermissionsBitField.Flags.BanMembers)) {
      return interaction.reply({
        content: "❌ I do not have permission to unban members.",
        ephemeral: true,
      });
    }

    const userInput = interaction.options.getString("user");
    const reason = interaction.options.getString("reason") || "No reason provided.";

    try {
      // Fetch the list of banned users
      const bans = await interaction.guild.bans.fetch();
      const bannedUser = bans.find((ban) => {
        // Check if the input matches the user ID or username#discriminator
        return ban.user.id === userInput || ban.user.tag === userInput;
      });

      if (!bannedUser) {
        return interaction.reply({
          content: "❌ The specified user is not banned.",
          ephemeral: true,
        });
      }

      // Unban the user
      await interaction.guild.members.unban(bannedUser.user, reason);

      await interaction.reply({
        content: `✅ Successfully unbanned ${bannedUser.user.tag}. Reason: ${reason}`,
        ephemeral: true, // Only the user who executed the command can see this message
      });
    } catch (error) {
      console.error(error);
      await interaction.reply({
        content: "❌ An error occurred while trying to unban the user.",
        ephemeral: true,
      });
    }
  },
};