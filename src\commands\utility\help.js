const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('help')
        .setDescription('Display all available bot commands')
        .addStringOption(option =>
            option.setName('category')
                .setDescription('Show commands from a specific category')
                .setRequired(false)
                .addChoices(
                    { name: '🛡️ Admin Commands', value: 'admin' },
                    { name: '⚖️ Moderation Commands', value: 'moderation' },
                    { name: '🔧 Tools Commands', value: 'tools' },
                    { name: '🔨 Utility Commands', value: 'utility' }
                ))
        .addStringOption(option =>
            option.setName('command')
                .setDescription('Get detailed information about a specific command')
                .setRequired(false)),

    async execute(interaction) {
        const category = interaction.options.getString('category');
        const specificCommand = interaction.options.getString('command');

        if (specificCommand) {
            await showSpecificCommand(interaction, specificCommand);
        } else if (category) {
            await showCategoryCommands(interaction, category);
        } else {
            await showMainHelp(interaction);
        }
    }
};

// Main help overview
async function showMainHelp(interaction) {
    const embed = new EmbedBuilder()
        .setColor('#0099ff')
        .setTitle('🤖 Bot Commands Help')
        .setDescription('Welcome to the help system! Here are all available command categories:')
        .setThumbnail(interaction.client.user.displayAvatarURL())
        .addFields(
            {
                name: '🛡️ Admin Commands (1)',
                value: '`setstatus`\nAdministrator-only commands for server management',
                inline: true
            },
            {
                name: '⚖️ Moderation Commands (22)',
                value: '`warn`, `ban`, `kick`, `timeout`, `purge`, `lockdown`, and more\nModeration tools for server safety',
                inline: true
            },
            {
                name: '🔧 Tools Commands (2)',
                value: '`ping`, `embed`\nUtility tools and testing commands',
                inline: true
            },
            {
                name: '🔨 Utility Commands (2)',
                value: '`command`, `embedcreator`\nCustom commands and embed creation',
                inline: true
            },
            {
                name: '📖 How to Use',
                value: '• Use `/help category:<category>` to see commands in a specific category\n• Use `/help command:<command_name>` for detailed command info\n• Click the buttons below to navigate categories',
                inline: false
            }
        )
        .setFooter({ text: 'Use the buttons below to explore each category' })
        .setTimestamp();

    const row = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('help_admin')
                .setLabel('Admin')
                .setStyle(ButtonStyle.Primary)
                .setEmoji('🛡️'),
            new ButtonBuilder()
                .setCustomId('help_moderation')
                .setLabel('Moderation')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('⚖️'),
            new ButtonBuilder()
                .setCustomId('help_tools')
                .setLabel('Tools')
                .setStyle(ButtonStyle.Success)
                .setEmoji('🔧'),
            new ButtonBuilder()
                .setCustomId('help_utility')
                .setLabel('Utility')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('🔨')
        );

    await interaction.reply({
        embeds: [embed],
        components: [row],
        ephemeral: true
    });
}

// Show commands for a specific category
async function showCategoryCommands(interaction, category) {
    const commands = getCommandsByCategory(category);

    const embed = new EmbedBuilder()
        .setColor(getCategoryColor(category))
        .setTitle(`${getCategoryEmoji(category)} ${getCategoryName(category)}`)
        .setDescription(`Here are all the ${category} commands available:`)
        .setThumbnail(interaction.client.user.displayAvatarURL())
        .setFooter({ text: `Total: ${commands.length} commands` })
        .setTimestamp();

    // Add commands in chunks to avoid embed limits
    let commandList = '';
    commands.forEach(cmd => {
        const cmdText = `**/${cmd.name}** - ${cmd.description}\n`;
        if (commandList.length + cmdText.length > 1024) {
            embed.addFields({ name: 'Commands', value: commandList, inline: false });
            commandList = cmdText;
        } else {
            commandList += cmdText;
        }
    });

    if (commandList) {
        embed.addFields({ name: 'Commands', value: commandList, inline: false });
    }

    const row = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('help_back')
                .setLabel('Back to Main')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('⬅️')
        );

    await interaction.reply({
        embeds: [embed],
        components: [row],
        ephemeral: true
    });
}

// Show detailed information about a specific command
async function showSpecificCommand(interaction, commandName) {
    const commandInfo = getCommandInfo(commandName);

    if (!commandInfo) {
        return interaction.reply({
            content: `❌ Command \`${commandName}\` not found. Use \`/help\` to see all available commands.`,
            ephemeral: true
        });
    }

    const embed = new EmbedBuilder()
        .setColor('#00ff00')
        .setTitle(`📋 Command: /${commandInfo.name}`)
        .setDescription(commandInfo.description)
        .addFields(
            { name: '📂 Category', value: commandInfo.category, inline: true },
            { name: '🔑 Permissions', value: commandInfo.permissions || 'None', inline: true },
            { name: '📝 Usage', value: commandInfo.usage || `\`/${commandInfo.name}\``, inline: false }
        )
        .setFooter({ text: 'Use /help to see all commands' })
        .setTimestamp();

    if (commandInfo.examples) {
        embed.addFields({ name: '💡 Examples', value: commandInfo.examples, inline: false });
    }

    await interaction.reply({
        embeds: [embed],
        ephemeral: true
    });
}

// Helper functions
function getCategoryColor(category) {
    const colors = {
        admin: '#ff0000',
        moderation: '#ff9900',
        tools: '#00ff00',
        utility: '#0099ff'
    };
    return colors[category] || '#0099ff';
}

function getCategoryEmoji(category) {
    const emojis = {
        admin: '🛡️',
        moderation: '⚖️',
        tools: '🔧',
        utility: '🔨'
    };
    return emojis[category] || '📁';
}

function getCategoryName(category) {
    const names = {
        admin: 'Admin Commands',
        moderation: 'Moderation Commands',
        tools: 'Tools Commands',
        utility: 'Utility Commands'
    };
    return names[category] || 'Commands';
}

function getCommandsByCategory(category) {
    const commands = {
        admin: [
            { name: 'setstatus', description: 'Change the bot\'s status and activity' }
        ],
        moderation: [
            { name: 'ban', description: 'Ban a user from the server' },
            { name: 'come', description: 'Call a member to come (Arabic)' },
            { name: 'delete', description: 'Delete messages or content' },
            { name: 'giverole', description: 'Give a role to a user' },
            { name: 'info', description: 'Display server information panel' },
            { name: 'join', description: 'Join voice channel commands' },
            { name: 'kick', description: 'Kick a user from the server' },
            { name: 'leave', description: 'Leave voice channel commands' },
            { name: 'lock', description: 'Lock a channel' },
            { name: 'lockdown', description: 'Lock/unlock server or channels' },
            { name: 'moveall', description: 'Move all users from voice channel' },
            { name: 'moveme', description: 'Move yourself to another voice channel' },
            { name: 'moveuser', description: 'Move a user to another voice channel' },
            { name: 'mute', description: 'Mute a user' },
            { name: 'nickname', description: 'Manage user nicknames' },
            { name: 'purge', description: 'Bulk delete messages with filters' },
            { name: 'slowmode', description: 'Set channel slowmode' },
            { name: 'tempban', description: 'Temporarily ban users with auto-unban' },
            { name: 'ticket', description: 'Manage support tickets' },
            { name: 'timeout', description: 'Timeout a user for specified duration' },
            { name: 'unban', description: 'Unban a user from the server' },
            { name: 'unlock', description: 'Unlock a channel' },
            { name: 'unmute', description: 'Unmute a user' },
            { name: 'untimeout', description: 'Remove timeout from a user' },
            { name: 'warn', description: 'Issue warnings with auto-escalation' },
            { name: 'warnings', description: 'View user warning history' }
        ],
        tools: [
            { name: 'ping', description: 'Check bot latency and response time' },
            { name: 'embed', description: 'Create a sample embed message' }
        ],
        utility: [
            { name: 'command', description: 'Create and manage custom commands' },
            { name: 'embedcreator', description: 'Interactive embed builder' }
        ]
    };

    return commands[category] || [];
}

function getCommandInfo(commandName) {
    const commandDetails = {
        // Admin Commands
        setstatus: {
            name: 'setstatus',
            description: 'Change the bot\'s status and activity display',
            category: 'Admin',
            permissions: 'Administrator',
            usage: '`/setstatus status:<status> activity-type:<type> activity-name:<name>`',
            examples: '`/setstatus status:online activity-type:0 activity-name:"Moderating Server"`'
        },

        // Moderation Commands
        warn: {
            name: 'warn',
            description: 'Issue warnings to users with automatic escalation system',
            category: 'Moderation',
            permissions: 'Moderate Members',
            usage: '`/warn user:<user> reason:<reason> [dm:<true/false>]`',
            examples: '`/warn user:@BadUser reason:"Spam in chat" dm:true`\n**Auto-escalation:** 3 warnings = 24h timeout, 5 warnings = ban'
        },
        warnings: {
            name: 'warnings',
            description: 'View and manage user warning history',
            category: 'Moderation',
            permissions: 'Moderate Members',
            usage: '`/warnings user:<user> [show_details:<true/false>]`',
            examples: '`/warnings user:@User show_details:true`'
        },
        tempban: {
            name: 'tempban',
            description: 'Temporarily ban users with automatic unbanning',
            category: 'Moderation',
            permissions: 'Ban Members',
            usage: '`/tempban user:<user> duration:<time> [reason:<reason>] [delete_messages:<days>]`',
            examples: '`/tempban user:@BadUser duration:2d reason:"Harassment"`\n**Formats:** 30m, 2h, 1d, 1w'
        },
        purge: {
            name: 'purge',
            description: 'Advanced bulk message deletion with smart filtering',
            category: 'Moderation',
            permissions: 'Manage Messages',
            usage: '`/purge amount:<1-100> [user:<user>] [filter:<type>] [contains:<text>]`',
            examples: '`/purge amount:50 filter:bots`\n`/purge amount:20 user:@SpamBot`\n`/purge amount:10 contains:"bad word"`'
        },
        timeout: {
            name: 'timeout',
            description: 'Timeout a user for a specified duration',
            category: 'Moderation',
            permissions: 'Moderate Members',
            usage: '`/timeout user:<user> duration:<minutes> [reason:<reason>]`',
            examples: '`/timeout user:@BadUser duration:60 reason:"Calm down"`'
        },
        ban: {
            name: 'ban',
            description: 'Permanently ban a user from the server',
            category: 'Moderation',
            permissions: 'Ban Members',
            usage: '`/ban user:<user> [reason:<reason>] [delete_messages:<days>]`',
            examples: '`/ban user:@BadUser reason:"Repeated violations" delete_messages:7`'
        },
        kick: {
            name: 'kick',
            description: 'Kick a user from the server',
            category: 'Moderation',
            permissions: 'Kick Members',
            usage: '`/kick user:<user> [reason:<reason>]`',
            examples: '`/kick user:@BadUser reason:"Rule violation"`'
        },
        slowmode: {
            name: 'slowmode',
            description: 'Set channel slowmode with smart duration formatting',
            category: 'Moderation',
            permissions: 'Manage Channels',
            usage: '`/slowmode seconds:<0-21600> [reason:<reason>]`',
            examples: '`/slowmode seconds:30 reason:"Calm down chat"`\n`/slowmode seconds:0` - Remove slowmode'
        },
        lockdown: {
            name: 'lockdown',
            description: 'Lock or unlock server channels for security',
            category: 'Moderation',
            permissions: 'Administrator',
            usage: '`/lockdown action:<lock/unlock> [reason:<reason>]`',
            examples: '`/lockdown action:lock_server reason:"Emergency"`'
        },

        // Tools Commands
        ping: {
            name: 'ping',
            description: 'Check bot latency and response time',
            category: 'Tools',
            permissions: 'None',
            usage: '`/ping`',
            examples: '`/ping` - Shows API latency and client ping'
        },
        embed: {
            name: 'embed',
            description: 'Create a sample embed message for testing',
            category: 'Tools',
            permissions: 'None',
            usage: '`/embed`',
            examples: '`/embed` - Displays a sample embed with various fields'
        },

        // Utility Commands
        command: {
            name: 'command',
            description: 'Create and manage custom slash commands',
            category: 'Utility',
            permissions: 'Manage Messages',
            usage: '`/command create` | `/command list` | `/command edit <name>` | `/command delete <name>`',
            examples: '`/command create` - Interactive command creation\n`/command list` - View all custom commands'
        },
        embedcreator: {
            name: 'embedcreator',
            description: 'Interactive embed builder with live preview',
            category: 'Utility',
            permissions: 'Manage Messages',
            usage: '`/embedcreator create channel:<channel>` | `/embedcreator edit message_id:<id> channel:<channel>`',
            examples: '`/embedcreator create channel:#announcements`'
        },

        // Additional moderation commands
        info: {
            name: 'info',
            description: 'Display server information panel with interactive buttons',
            category: 'Moderation',
            permissions: 'None',
            usage: '`/info`',
            examples: '`/info` - Shows server info panel with navigation buttons'
        },
        come: {
            name: 'come',
            description: 'Call a member to come (sends DM in Arabic)',
            category: 'Moderation',
            permissions: 'Moderate Members',
            usage: '`/come user:<user>`',
            examples: '`/come user:@Member` - Sends Arabic DM to user'
        },
        nickname: {
            name: 'nickname',
            description: 'Manage user nicknames and cleanup inappropriate names',
            category: 'Moderation',
            permissions: 'Manage Nicknames',
            usage: '`/nickname set user:<user> nickname:<name>` | `/nickname reset user:<user>` | `/nickname cleanup filter:<type>`',
            examples: '`/nickname set user:@User nickname:"New Name"`\n`/nickname cleanup filter:special`'
        }
    };

    return commandDetails[commandName.toLowerCase()] || null;
}
