const { <PERSON>lash<PERSON><PERSON>mand<PERSON>uilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('purge')
        .setDescription('Bulk delete messages')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageMessages)
        .addIntegerOption(option =>
            option.setName('amount')
                .setDescription('Number of messages to delete (1-100)')
                .setRequired(true)
                .setMinValue(1)
                .setMaxValue(100))
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Only delete messages from this user')
                .setRequired(false))
        .addStringOption(option =>
            option.setName('filter')
                .setDescription('Filter messages by type')
                .setRequired(false)
                .addChoices(
                    { name: 'All Messages', value: 'all' },
                    { name: 'Bot Messages Only', value: 'bots' },
                    { name: 'User Messages Only', value: 'users' },
                    { name: 'Messages with Attachments', value: 'attachments' },
                    { name: 'Messages with Links', value: 'links' },
                    { name: 'Messages with Embeds', value: 'embeds' }
                ))
        .addStringOption(option =>
            option.setName('contains')
                .setDescription('Only delete messages containing this text')
                .setRequired(false)
                .setMaxLength(100)),

    async execute(interaction) {
        const amount = interaction.options.getInteger('amount');
        const targetUser = interaction.options.getUser('user');
        const filter = interaction.options.getString('filter') || 'all';
        const contains = interaction.options.getString('contains');

        // Check bot permissions
        if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageMessages)) {
            return interaction.reply({
                content: '❌ I need the "Manage Messages" permission to purge messages.',
                ephemeral: true
            });
        }

        await interaction.deferReply({ ephemeral: true });

        try {
            // Fetch messages
            const messages = await interaction.channel.messages.fetch({ limit: 100 });
            
            // Filter messages based on criteria
            let messagesToDelete = Array.from(messages.values());

            // Filter by user
            if (targetUser) {
                messagesToDelete = messagesToDelete.filter(msg => msg.author.id === targetUser.id);
            }

            // Filter by type
            switch (filter) {
                case 'bots':
                    messagesToDelete = messagesToDelete.filter(msg => msg.author.bot);
                    break;
                case 'users':
                    messagesToDelete = messagesToDelete.filter(msg => !msg.author.bot);
                    break;
                case 'attachments':
                    messagesToDelete = messagesToDelete.filter(msg => msg.attachments.size > 0);
                    break;
                case 'links':
                    messagesToDelete = messagesToDelete.filter(msg => 
                        msg.content.includes('http://') || msg.content.includes('https://'));
                    break;
                case 'embeds':
                    messagesToDelete = messagesToDelete.filter(msg => msg.embeds.length > 0);
                    break;
                // 'all' requires no additional filtering
            }

            // Filter by content
            if (contains) {
                messagesToDelete = messagesToDelete.filter(msg => 
                    msg.content.toLowerCase().includes(contains.toLowerCase()));
            }

            // Limit to requested amount
            messagesToDelete = messagesToDelete.slice(0, amount);

            // Filter out messages older than 14 days (Discord limitation)
            const twoWeeksAgo = Date.now() - (14 * 24 * 60 * 60 * 1000);
            const recentMessages = messagesToDelete.filter(msg => msg.createdTimestamp > twoWeeksAgo);
            const oldMessages = messagesToDelete.filter(msg => msg.createdTimestamp <= twoWeeksAgo);

            if (recentMessages.length === 0) {
                const embed = new EmbedBuilder()
                    .setColor('#ff6b6b')
                    .setTitle('❌ No Messages to Delete')
                    .setDescription('No messages found matching your criteria, or all messages are older than 14 days.')
                    .addFields(
                        { name: 'Criteria', value: generateCriteriaText(amount, targetUser, filter, contains), inline: false }
                    )
                    .setTimestamp();

                return interaction.editReply({ embeds: [embed] });
            }

            // Delete messages
            let deletedCount = 0;
            
            if (recentMessages.length === 1) {
                // Delete single message
                await recentMessages[0].delete();
                deletedCount = 1;
            } else {
                // Bulk delete
                const deleted = await interaction.channel.bulkDelete(recentMessages, true);
                deletedCount = deleted.size;
            }

            // Create success embed
            const embed = new EmbedBuilder()
                .setColor('#4CAF50')
                .setTitle('✅ Messages Purged')
                .setDescription(`Successfully deleted **${deletedCount}** message(s).`)
                .addFields(
                    { name: 'Channel', value: `${interaction.channel.name}`, inline: true },
                    { name: 'Moderator', value: `${interaction.user.tag}`, inline: true },
                    { name: 'Criteria', value: generateCriteriaText(amount, targetUser, filter, contains), inline: false }
                )
                .setTimestamp();

            if (oldMessages.length > 0) {
                embed.addFields({
                    name: '⚠️ Note',
                    value: `${oldMessages.length} message(s) were skipped because they are older than 14 days.`,
                    inline: false
                });
            }



            await interaction.editReply({ embeds: [embed] });

            // Auto-delete the response after 10 seconds to keep channel clean
            setTimeout(async () => {
                try {
                    await interaction.deleteReply();
                } catch (error) {
                    // Response might already be deleted
                }
            }, 10000);

        } catch (error) {
            console.error('Error in purge command:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff6b6b')
                .setTitle('❌ Purge Failed')
                .setDescription('An error occurred while trying to delete messages.')
                .addFields(
                    { name: 'Error', value: error.message || 'Unknown error', inline: false }
                )
                .setTimestamp();

            await interaction.editReply({ embeds: [errorEmbed] });
        }
    }
};

// Generate criteria text for embeds
function generateCriteriaText(amount, targetUser, filter, contains) {
    const criteria = [];
    
    criteria.push(`**Amount:** ${amount} messages`);
    
    if (targetUser) {
        criteria.push(`**User:** ${targetUser.tag}`);
    }
    
    if (filter !== 'all') {
        const filterNames = {
            'bots': 'Bot messages only',
            'users': 'User messages only',
            'attachments': 'Messages with attachments',
            'links': 'Messages with links',
            'embeds': 'Messages with embeds'
        };
        criteria.push(`**Filter:** ${filterNames[filter]}`);
    }
    
    if (contains) {
        criteria.push(`**Contains:** "${contains}"`);
    }
    
    return criteria.join('\n');
}
