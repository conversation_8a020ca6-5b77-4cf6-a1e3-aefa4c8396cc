const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('nickname')
        .setDescription('Manage user nicknames')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageNicknames)
        .addSubcommand(subcommand =>
            subcommand
                .setName('set')
                .setDescription('Set a user\'s nickname')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('The user to set nickname for')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('nickname')
                        .setDescription('The new nickname (leave empty to remove)')
                        .setRequired(false)
                        .setMaxLength(32)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('reset')
                .setDescription('Reset a user\'s nickname to their username')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('The user to reset nickname for')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('cleanup')
                .setDescription('Clean up inappropriate nicknames')
                .addStringOption(option =>
                    option.setName('filter')
                        .setDescription('What to clean up')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Special Characters', value: 'special' },
                            { name: 'Invisible Characters', value: 'invisible' },
                            { name: 'Hoisting Characters', value: 'hoisting' },
                            { name: 'All Issues', value: 'all' }
                        ))),

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();

        switch (subcommand) {
            case 'set':
                await handleSetNickname(interaction);
                break;
            case 'reset':
                await handleResetNickname(interaction);
                break;
            case 'cleanup':
                await handleCleanupNicknames(interaction);
                break;
        }
    }
};

// Handle set nickname subcommand
async function handleSetNickname(interaction) {
    const user = interaction.options.getUser('user');
    const nickname = interaction.options.getString('nickname');
    
    const member = interaction.guild.members.cache.get(user.id);
    if (!member) {
        return interaction.reply({
            content: '❌ User is not in this server.',
            ephemeral: true
        });
    }

    // Check if user is trying to change their own nickname
    if (user.id === interaction.user.id && !interaction.member.permissions.has(PermissionFlagsBits.ChangeNickname)) {
        return interaction.reply({
            content: '❌ You don\'t have permission to change your own nickname.',
            ephemeral: true
        });
    }

    // Check if user is trying to change someone with higher or equal permissions
    if (member.roles.highest.position >= interaction.member.roles.highest.position && user.id !== interaction.user.id) {
        return interaction.reply({
            content: '❌ You cannot change the nickname of someone with equal or higher permissions!',
            ephemeral: true
        });
    }

    // Check if member is manageable
    if (!member.manageable) {
        return interaction.reply({
            content: '❌ I cannot change this user\'s nickname! They may have higher permissions than me.',
            ephemeral: true
        });
    }

    try {
        const oldNickname = member.nickname || member.user.username;
        const newNickname = nickname || null;

        await member.setNickname(newNickname, `Nickname changed by ${interaction.user.tag}`);

        const embed = new EmbedBuilder()
            .setColor('#4CAF50')
            .setTitle('✅ Nickname Changed')
            .addFields(
                { name: 'User', value: `${user.tag} (${user.id})`, inline: true },
                { name: 'Moderator', value: `${interaction.user.tag}`, inline: true },
                { name: 'Old Nickname', value: oldNickname, inline: false },
                { name: 'New Nickname', value: newNickname || user.username, inline: false }
            )
            .setThumbnail(user.displayAvatarURL({ dynamic: true }))
            .setTimestamp();



        await interaction.reply({ embeds: [embed] });

    } catch (error) {
        console.error('Error changing nickname:', error);
        await interaction.reply({
            content: '❌ An error occurred while changing the nickname.',
            ephemeral: true
        });
    }
}

// Handle reset nickname subcommand
async function handleResetNickname(interaction) {
    const user = interaction.options.getUser('user');
    
    const member = interaction.guild.members.cache.get(user.id);
    if (!member) {
        return interaction.reply({
            content: '❌ User is not in this server.',
            ephemeral: true
        });
    }

    if (!member.nickname) {
        return interaction.reply({
            content: '❌ This user doesn\'t have a nickname to reset.',
            ephemeral: true
        });
    }

    // Check permissions (same as set nickname)
    if (member.roles.highest.position >= interaction.member.roles.highest.position && user.id !== interaction.user.id) {
        return interaction.reply({
            content: '❌ You cannot reset the nickname of someone with equal or higher permissions!',
            ephemeral: true
        });
    }

    if (!member.manageable) {
        return interaction.reply({
            content: '❌ I cannot change this user\'s nickname! They may have higher permissions than me.',
            ephemeral: true
        });
    }

    try {
        const oldNickname = member.nickname;
        await member.setNickname(null, `Nickname reset by ${interaction.user.tag}`);

        const embed = new EmbedBuilder()
            .setColor('#4CAF50')
            .setTitle('✅ Nickname Reset')
            .addFields(
                { name: 'User', value: `${user.tag} (${user.id})`, inline: true },
                { name: 'Moderator', value: `${interaction.user.tag}`, inline: true },
                { name: 'Old Nickname', value: oldNickname, inline: false },
                { name: 'New Display Name', value: user.username, inline: false }
            )
            .setThumbnail(user.displayAvatarURL({ dynamic: true }))
            .setTimestamp();



        await interaction.reply({ embeds: [embed] });

    } catch (error) {
        console.error('Error resetting nickname:', error);
        await interaction.reply({
            content: '❌ An error occurred while resetting the nickname.',
            ephemeral: true
        });
    }
}

// Handle cleanup nicknames subcommand
async function handleCleanupNicknames(interaction) {
    const filter = interaction.options.getString('filter');

    await interaction.deferReply();

    try {
        const members = await interaction.guild.members.fetch();
        const problematicMembers = [];

        for (const [memberId, member] of members) {
            if (!member.nickname) continue;
            if (!member.manageable) continue;

            const nickname = member.nickname;
            let shouldClean = false;
            let reason = '';

            switch (filter) {
                case 'special':
                    if (/[^\w\s\-_]/.test(nickname)) {
                        shouldClean = true;
                        reason = 'Contains special characters';
                    }
                    break;
                case 'invisible':
                    if (/[\u200B-\u200D\uFEFF\u00A0]/.test(nickname) || nickname.trim() === '') {
                        shouldClean = true;
                        reason = 'Contains invisible characters';
                    }
                    break;
                case 'hoisting':
                    if (/^[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]/.test(nickname)) {
                        shouldClean = true;
                        reason = 'Starts with hoisting character';
                    }
                    break;
                case 'all':
                    if (/[^\w\s\-_]/.test(nickname) || 
                        /[\u200B-\u200D\uFEFF\u00A0]/.test(nickname) || 
                        nickname.trim() === '' ||
                        /^[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]/.test(nickname)) {
                        shouldClean = true;
                        reason = 'Multiple issues detected';
                    }
                    break;
            }

            if (shouldClean) {
                problematicMembers.push({ member, reason, oldNickname: nickname });
            }
        }

        if (problematicMembers.length === 0) {
            const embed = new EmbedBuilder()
                .setColor('#4CAF50')
                .setTitle('✅ No Issues Found')
                .setDescription(`No problematic nicknames found for filter: **${filter}**`)
                .setTimestamp();

            return interaction.editReply({ embeds: [embed] });
        }

        // Clean up the nicknames
        let cleanedCount = 0;
        const cleanedMembers = [];

        for (const { member, reason, oldNickname } of problematicMembers) {
            try {
                await member.setNickname(null, `Nickname cleanup: ${reason}`);
                cleanedCount++;
                cleanedMembers.push({ member, reason, oldNickname });
            } catch (error) {
                console.error(`Failed to clean nickname for ${member.user.tag}:`, error);
            }
        }

        const embed = new EmbedBuilder()
            .setColor('#4CAF50')
            .setTitle('🧹 Nickname Cleanup Complete')
            .setDescription(`Successfully cleaned **${cleanedCount}** out of **${problematicMembers.length}** problematic nicknames.`)
            .addFields(
                { name: 'Filter Used', value: filter, inline: true },
                { name: 'Moderator', value: `${interaction.user.tag}`, inline: true }
            )
            .setTimestamp();

        // Add details of cleaned nicknames (limit to 10)
        if (cleanedMembers.length > 0) {
            const details = cleanedMembers.slice(0, 10).map(({ member, reason, oldNickname }) => 
                `**${member.user.tag}:** "${oldNickname}" (${reason})`
            ).join('\n');

            embed.addFields({ 
                name: 'Cleaned Nicknames', 
                value: details + (cleanedMembers.length > 10 ? `\n... and ${cleanedMembers.length - 10} more` : ''),
                inline: false 
            });
        }



        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        console.error('Error in nickname cleanup:', error);
        
        const errorEmbed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Cleanup Failed')
            .setDescription('An error occurred while cleaning up nicknames.')
            .addFields(
                { name: 'Error', value: error.message || 'Unknown error', inline: false }
            )
            .setTimestamp();

        await interaction.editReply({ embeds: [errorEmbed] });
    }
}
