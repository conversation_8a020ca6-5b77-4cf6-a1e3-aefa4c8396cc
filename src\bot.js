require("dotenv").config(); // Load environment variables
const { Client, Collection, GatewayIntentBits, Events } = require("discord.js");
const fs = require("fs");

// Create the client and specify intents
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildVoiceStates,
    GatewayIntentBits.GuildModeration,
  ],
});

// Initialize collections
client.commands = new Collection();
client.commandArray = [];
client.events = new Collection();

// Clear require cache for handlers to ensure fresh loading
const clearHandlerCache = () => {
  const handlerPath = require.resolve('./functions/handlers/handleCommands.js');
  const customCommandsPath = require.resolve('./functions/handlers/handleCustomCommands.js');
  delete require.cache[handlerPath];
  delete require.cache[customCommandsPath];
};

// Clear cache before loading
clearHandlerCache();

// Load functions
const functionFolders = fs.readdirSync("./src/functions");
for (const folder of functionFolders) {
  const functionFiles = fs
    .readdirSync(`./src/functions/${folder}`)
    .filter((file) => file.endsWith(".js"));
  for (const file of functionFiles) {
    console.log(`Loading function from: ./functions/${folder}/${file}`);
    const func = require(`./functions/${folder}/${file}`);
    if (typeof func === "function") {
      func(client);
    } else {
      console.error(
        `Error: ./functions/${folder}/${file} does not export a function.`
      );
    }
  }
}

// Initialize handlers
client.handleCommands();
client.handleEvents();

// Initialize custom commands handler after regular commands are loaded
setTimeout(() => {
  if (client.handleCustomCommands) {
    client.handleCustomCommands.registerCustomCommands();
  }
}, 1000);

// Ready event
client.once(Events.ClientReady, () => {
  console.log(`✅ Logged in as ${client.user.tag}!`);

  // Initialize tempban checker
  try {
    const tempbanCommand = require('./commands/moderation/tempban.js');
    if (tempbanCommand.initTempbanChecker) {
      tempbanCommand.initTempbanChecker(client);
    }
  } catch (error) {
    console.error('Error initializing tempban checker:', error);
  }
});

// Error handling
client.on('error', error => {
  console.error('Discord client error:', error);
});

process.on('unhandledRejection', error => {
  console.error('Unhandled promise rejection:', error);
});

// Login
client.login(process.env.TOKEN);
