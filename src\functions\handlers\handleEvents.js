const fs = require("fs");
const path = require("path");

module.exports = (client) => {
  client.handleEvents = async () => {
    const eventFolders = fs.readdirSync("./src/events");

    for (const folder of eventFolders) {
      const fullPath = path.join("./src/events", folder);

      if (fs.lstatSync(fullPath).isDirectory()) { // Check if it's a directory
        const eventFiles = fs
          .readdirSync(fullPath)
          .filter((file) => file.endsWith(".js"));

        for (const file of eventFiles) {
          console.log(`Loading event: ${file}`); // Log each event file loading
          try {
            const event = require(path.join(__dirname, `../../events/${folder}/${file}`));
            if (event.once) {
              client.once(event.name, (...args) => event.execute(...args, client));
            } else {
              client.on(event.name, (...args) => event.execute(...args, client));
            }
            console.log(`Event: ${event.name} has been registered.`);
          } catch (error) {
            console.error(
              `Error loading event at ./src/events/${folder}/${file}:`,
              error
            );
          }
        }
      } else {
        console.warn(`Skipping non-directory entry: ${folder}`);
      }
    }
  };
};
