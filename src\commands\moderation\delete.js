const { SlashCommandBuilder } = require('@discordjs/builders');
const { PermissionsBitField } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('delete')
        .setDescription('Delete a specified number of messages (Mods only)')
        .addIntegerOption(option =>
            option
                .setName('amount')
                .setDescription('The number of messages to delete (1-100)')
                .setRequired(true)
        ),
    async execute(interaction) {
        const amount = interaction.options.getInteger('amount');

        // Check if the user has "Manage Messages" permission
        if (!interaction.member.permissions.has(PermissionsBitField.Flags.ManageMessages)) {
            return interaction.reply({
                content: '❌ You do not have permission to use this command.',
                ephemeral: true,
            });
        }

        // Check if the bot has "Manage Messages" and "Read Message History" permissions
        const botMember = interaction.guild.members.resolve(interaction.client.user);
        if (!botMember.permissions.has(PermissionsBitField.Flags.ManageMessages)) {
            return interaction.reply({
                content: '❌ I do not have permission to delete messages.',
                ephemeral: true,
            });
        }
        if (!botMember.permissions.has(PermissionsBitField.Flags.ReadMessageHistory)) {
            return interaction.reply({
                content: '❌ I need the "Read Message History" permission to delete messages.',
                ephemeral: true,
            });
        }

        await interaction.deferReply({ ephemeral: true });

        try {
            // Fetch messages (Discord only allows up to 100 at a time)
            const fetchedMessages = await interaction.channel.messages.fetch({ limit: amount });

            // Filter out old messages (older than 14 days)
            const deletableMessages = fetchedMessages.filter(msg => (Date.now() - msg.createdTimestamp) < 1209600000); // 14 days in ms

            if (deletableMessages.size === 0) {
                return interaction.editReply({
                    content: '❌ No messages found that can be deleted (Messages older than 14 days cannot be deleted).',
                });
            }

            // Delete messages
            const deletedMessages = await interaction.channel.bulkDelete(deletableMessages, true);
            await interaction.editReply({
                content: `✅ Successfully deleted ${deletedMessages.size} messages.`,
            });

        } catch (error) {
            console.error(error);
            await interaction.editReply({
                content: '❌ An error occurred while deleting messages. Ensure messages are not older than 14 days.',
            });
        }
    },
};
